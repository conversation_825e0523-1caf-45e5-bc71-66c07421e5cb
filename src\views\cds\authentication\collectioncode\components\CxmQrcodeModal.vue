<template>
    <BasicModal v-bind="$attrs" @register="registerModal" title="采血码"  :show-cancel-btn="false" :show-ok-btn="false" :defaultFullscreen="false"  :width="250" :height="200" destroyOnClose>
      
      <QrCode :value="qrCodeUrl" />
       
    </BasicModal>
  </template>
  
  <script lang="ts" setup name="user-quit-modal">
    import { ref, toRaw } from 'vue';
    import { QrCode, QrCodeActionType } from '/@/components/Qrcode/index';
    import { BasicModal, useModalInner } from '/@/components/Modal';
  
  
    import { Modal } from 'ant-design-vue';
  
    // 声明Emits
    const emit = defineEmits(['success', 'register']);
    const queryParam = ref();
    const qrCodeUrl =ref('');
  
  const [registerModal, { closeModal,}] = useModalInner((data) => {
    qrCodeUrl.value=data.cxmcode;
  
    });
    
  
  
  </script>
  
  <style scoped lang="less">
  :deep(.ant-popover-inner-content){
    width: 185px !important;
  }
  </style>
  