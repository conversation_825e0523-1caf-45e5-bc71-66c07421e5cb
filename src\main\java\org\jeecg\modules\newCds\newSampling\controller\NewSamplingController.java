package org.jeecg.modules.newCds.newSampling.controller;

import java.util.Arrays;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.newCds.newSampling.child.entity.LianTask;
import org.jeecg.modules.newCds.newSampling.child.entity.Popular;
import org.jeecg.modules.newCds.newSampling.child.entity.ShiTask;
import org.jeecg.modules.newCds.newSampling.child.entity.TuanTask;
import org.jeecg.modules.newCds.newSampling.child.service.ILianTaskService;
import org.jeecg.modules.newCds.newSampling.child.service.IShiTaskService;
import org.jeecg.modules.newCds.newSampling.child.service.ITuanTaskService;
import org.jeecg.modules.newCds.newSampling.entity.dto.SampleDto;
import org.jeecg.modules.newCds.newSampling.entity.pojo.NewSampling;
import org.jeecg.modules.newCds.newSampling.entity.vo.SamplingReportVO;
import org.jeecg.modules.newCds.newSampling.service.INewSamplingService;
import org.jeecg.modules.newCds.newSampling.service.ISamplingTaskReportService;
import org.jeecg.modules.newCds.newSampling.service.TaskStatusService;
import org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 慢病抽样任务
 * @Author: jeecg-boot
 * @Date: 2025-06-30
 * @Version: V1.0
 */
@Tag(name = "慢病抽样任务")
@RestController
@RequestMapping("/newSampling/newSampling")
@Slf4j
public class NewSamplingController extends JeecgController<NewSampling, INewSamplingService> {
    @Autowired
    private INewSamplingService newSamplingService;

    @Autowired
    private ISamplingTaskReportService samplingTaskReportService;

    /**
     * 验证用户级别权限
     * @param requiredLevel 需要的权限级别（1=兵团级, 2=师级, 3=团级, 4=连级）
     * @param operationName 操作名称（用于错误提示）
     */
    private void validateLevelPermission(String requiredLevel, String operationName) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null || user.getZonecode() == null) {
            throw new RuntimeException("用户信息获取失败，无法进行权限验证");
        }

        // 获取用户地区级别
        int userLevel = getUserZoneLevel(user.getZonecode());
        int required = Integer.parseInt(requiredLevel);

        // 用户级别必须小于等于所需级别（级别数字越小权限越高）
        if (userLevel > required) {
            throw new RuntimeException(String.format("权限不足，%s需要%s权限，当前用户为%s权限",
                operationName, getLevelName(required), getLevelName(userLevel)));
        }
    }

    /**
     * 根据地区编码获取用户级别
     * @param zonecode 地区编码
     * @return 级别（1=兵团级, 2=师级, 3=团级, 4=连级）
     */
    private int getUserZoneLevel(String zonecode) {
        if (zonecode == null || zonecode.length() < 9) {
            return 4; // 默认最低级别
        }

        // 根据地区编码长度和规则判断级别
        // 这里需要根据实际的地区编码规则来实现
        // 示例实现：
        String levelPart = zonecode.substring(6, 9);
        if ("000".equals(levelPart)) {
            return 1; // 兵团级
        } else if (zonecode.endsWith("000")) {
            return 2; // 师级
        } else if (zonecode.endsWith("00")) {
            return 3; // 团级
        } else {
            return 4; // 连级
        }
    }

    /**
     * 获取级别名称
     * @param level 级别数字
     * @return 级别名称
     */
    private String getLevelName(int level) {
        switch (level) {
            case 1: return "兵团级";
            case 2: return "师级";
            case 3: return "团级";
            case 4: return "连级";
            default: return "未知级别";
        }
    }
    @Autowired
    private IShiTaskService shiService;
    @Autowired
    private ITuanTaskService tuanService;
    @Autowired
    private ILianTaskService lianService;

    @Autowired
    private TaskStatusService taskStatusService;

    /**
     * 分页列表查询
     *
     * @param newSampling
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "慢病抽样任务-分页列表查询")
    @Operation(summary = "慢病抽样任务-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<NewSampling>> queryPageList(NewSampling newSampling,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
        // 获取当前登录用户
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        QueryWrapper<NewSampling> queryWrapper = QueryGenerator.initQueryWrapper(newSampling, req.getParameterMap());

        // 添加地区权限过滤：用户只能查看自己地区及下级地区的数据
        if (user != null && user.getZonecode() != null) {
            queryWrapper.likeRight("zonecode", user.getZonecode());
        }

        Page<NewSampling> page = new Page<NewSampling>(pageNo, pageSize);
        IPage<NewSampling> pageList = newSamplingService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 地区上报人数分页
     */
    @Operation(summary = "地区上报人数分页")
    @GetMapping(value = "/zoneList")
    public Result<IPage<Popular>> queryZonePageList(SampleDto dto,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize
    ) {
        List<Popular> vosList = newSamplingService.queryZoneList(dto);
        Page<Popular> pageList = new Page<>(pageNo, pageSize);
        int total = vosList != null ? vosList.size() : 0;
        pageList.setTotal(total).setRecords(vosList);
        return Result.OK(pageList);
    }

    /**
     * 地区上报数据分页 - 新接口，返回专门的VO
     */
    @Operation(summary = "地区上报数据分页")
    @GetMapping(value = "/zoneReportList")
    public Result<IPage<SamplingReportVO>> queryZoneReportPageList(SampleDto dto,
                                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize
    ) {
        List<SamplingReportVO> vosList = newSamplingService.queryZoneReportList(dto);
        Page<SamplingReportVO> pageList = new Page<>(pageNo, pageSize);
        int total = vosList != null ? vosList.size() : 0;
        pageList.setTotal(total).setRecords(vosList);
        return Result.OK(pageList);
    }

    @AutoLog(value = "兵团级抽样-确认抽样")
    @Operation(summary = "兵团级抽样-确认抽样")
    @RequestMapping(value = "/btSampleShi", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> btSampleShi(@RequestBody SampleDto dto) {
        // 验证兵团级权限
        validateLevelPermission("1", "兵团级抽样确认");
        String message = newSamplingService.btSampleShi(dto);
        return Result.OK(message);
    }

    @AutoLog(value = "师级抽样-确认抽样")
    @Operation(summary = "师级抽样-确认抽样")
    @RequestMapping(value = "/shiSampleTuan", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> shiSampleTuan(@RequestBody SampleDto dto) {
        // 验证师级权限
        validateLevelPermission("2", "师级抽样确认");
        String message = newSamplingService.shiSampleTuan(dto);
        return Result.OK(message);
    }

    @AutoLog(value = "团级抽样-确认抽样")
    @Operation(summary = "团级抽样-确认抽样")
    @RequestMapping(value = "/tuanSampleLian", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> tuanSampleLian(@RequestBody SampleDto dto) {
        // 验证团级权限
        validateLevelPermission("3", "团级抽样确认");
        String message = newSamplingService.tuanSampleLian(dto);
        return Result.OK(message);
    }

    @AutoLog(value = "更新任务进度")
    @Operation(summary = "更新任务进度")
    @RequestMapping(value = "/updateProgress", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> updateProgress(@RequestParam String taskId) {
        newSamplingService.updateTaskProgress(taskId);
        return Result.OK("进度更新成功");
    }

    @AutoLog(value = "获取任务已上报数量")
    @Operation(summary = "获取任务已上报数量")
    @GetMapping(value = "/reportedCount")
    public Result<Integer> getReportedCount(@RequestParam String taskId) {
        int count = samplingTaskReportService.countReportedDataByTaskId(taskId);
        return Result.OK(count);
    }




    /**
     * 师级分页
     */
    @AutoLog(value = "师级分页")
    @Operation(summary = "师级分页")
    @GetMapping(value = "/shiField")
    public Result<IPage<ShiTask>> queryShiListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<ShiTask> shiList = shiService.selectByMainId(id);
        IPage<ShiTask> page = new Page<>();
        page.setRecords(shiList);
        page.setTotal(shiList.size());
        return Result.OK(page);
    }

    /**
     * 团级分页
     */
    @AutoLog(value = "团级分页")
    @Operation(summary = "团级分页")
    @GetMapping(value = "/tuanField")
    public Result<IPage<TuanTask>> queryTuanListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<TuanTask> shiList = tuanService.selectByMainId(id);
        IPage<TuanTask> page = new Page<>();
        page.setRecords(shiList);
        page.setTotal(shiList.size());
        return Result.OK(page);
    }

    /**
     * 连级分页
     */
    @AutoLog(value = "连级分页")
    @Operation(summary = "连级分页")
    @GetMapping(value = "/lianField")
    public Result<IPage<LianTask>> queryLianListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<LianTask> shiList = lianService.selectByMainId(id);
        IPage<LianTask> page = new Page<>();
        page.setRecords(shiList);
        page.setTotal(shiList.size());
        return Result.OK(page);
    }


    /**
     * 添加
     *
     * @param newSampling
     * @return
     */
    @AutoLog(value = "慢病抽样任务-添加")
    @Operation(summary = "慢病抽样任务-添加")
    //@RequiresPermissions("newSampling:btpt_cds_sampling_task:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody NewSampling newSampling) {
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String result = newSamplingService.createSamplingTask(newSampling, user);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("添加抽样任务失败", e);
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param newSampling
     * @return
     */
    @AutoLog(value = "慢病抽样任务-编辑")
    @Operation(summary = "慢病抽样任务-编辑")
    //@RequiresPermissions("newSampling:btpt_cds_sampling_task:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody NewSampling newSampling) {
        newSamplingService.updateById(newSampling);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "慢病抽样任务-通过id删除")
    @Operation(summary = "慢病抽样任务-通过id删除")
    //@RequiresPermissions("newSampling:btpt_cds_sampling_task:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        newSamplingService.removeAllTask(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "慢病抽样任务-批量删除")
    @Operation(summary = "慢病抽样任务-批量删除")
    //@RequiresPermissions("newSampling:btpt_cds_sampling_task:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.newSamplingService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "慢病抽样任务-通过id查询")
    @Operation(summary = "慢病抽样任务-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<NewSampling> queryById(@RequestParam(name = "id", required = true) String id) {
        NewSampling newSampling = newSamplingService.getById(id);
        if (newSampling == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(newSampling);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param newSampling
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, NewSampling newSampling) {
        return super.exportXls(request, newSampling, NewSampling.class, "慢病抽样任务");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, NewSampling.class);
    }


//    /**
//     * 批量导入师级地区人口数
//     */
//    @RequestMapping(value = "/exportShiXls")
//    public ModelAndView exportShiXls(@RequestBody SampleDto dto) {
//        String fileName = "师级地区人口数批量上报模板";
//        //获取导出数据
//        List<Popular> exportList = new ArrayList<>();
//
//        //AutoPoi 导出Excel
//        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//        //此处设置的filename无效 ,前端会重更新设置一下
//        mv.addObject(NormalExcelConstants.FILE_NAME, fileName); //导出文件名
//        //实体类
//        mv.addObject(NormalExcelConstants.CLASS, Popular.class);
//        //设置导出参数，具体点进去看
//        ExportParams exportParams = new ExportParams(fileName, fileName);
//        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
//        //放入数据列表
//        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
//        //合计行
//        return mv;
//    }

    /**
     * 师人口上报模板下载
     */
    @RequestMapping(value = "/downLoadShiTemplate")
    public ModelAndView exportShiXls(HttpServletRequest request, HttpServletResponse response, SampleDto dto) {
        try {
            // 检查是否是模板下载请求
            String isTemplate = request.getParameter("isTemplate");
            boolean isNewTemplate = "true".equals(isTemplate);

            return newSamplingService.generateReportTemplate(isNewTemplate);
        } catch (Exception e) {
            // 如果生成模板失败，返回一个错误页面或者抛出异常
            throw new RuntimeException("模板下载失败：" + e.getMessage());
        }
    }

    /**
     * 数据上报
     *
     * @param samplingTaskReport
     * @return
     */
    @AutoLog(value = "数据上报-添加/编辑")
    @Operation(summary = "数据上报-添加/编辑")
    @PostMapping(value = "/reportData")
    public Result<String> reportData(@RequestBody SamplingTaskReport samplingTaskReport) {
        try {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String result = newSamplingService.reportDataBusiness(samplingTaskReport, user);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("数据上报失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 确认抽样完成
     */
    @AutoLog(value = "确认抽样完成")
    @Operation(summary = "确认抽样完成")
    @PostMapping(value = "/confirmSamplingComplete")
    public Result<String> confirmSamplingComplete(@RequestParam String taskId) {
        try {
            // 使用状态管理服务更新任务状态为已完成
            taskStatusService.updateToCompleted(taskId);
            return Result.OK("抽样确认完成");
        } catch (Exception e) {
            log.error("确认抽样完成失败", e);
            return Result.error("确认抽样完成失败: " + e.getMessage());
        }
    }



    /**
     * 获取上报数据列表
     *
     * @param taskId
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @Operation(summary = "获取上报数据列表")
    @GetMapping(value = "/getReportList")
    public Result<IPage<SamplingReportVO>> getReportList(@RequestParam(name = "taskId", required = true) String taskId,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         HttpServletRequest req) {
        try {
            IPage<SamplingReportVO> result = newSamplingService.getReportListWithPagination(taskId, pageNo, pageSize);
            return Result.OK(result);
        } catch (Exception e) {
            return Result.error("获取数据失败：" + e.getMessage());
        }
    }

    /**
     * 删除上报数据
     *
     * @param id
     * @return
     */
    @AutoLog(value = "删除上报数据")
    @Operation(summary = "删除上报数据")
    @DeleteMapping(value = "/deleteReport")
    public Result<String> deleteReport(@RequestParam(name = "id", required = true) String id) {
        try {
            samplingTaskReportService.removeById(id);
            return Result.OK("删除成功");
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }

}
