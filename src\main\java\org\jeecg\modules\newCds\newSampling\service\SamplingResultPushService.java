package org.jeecg.modules.newCds.newSampling.service;

import org.jeecg.modules.newCds.newSampling.entity.SamplingResult;
import org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport;

import java.util.List;

/**
 * 抽样结果推送服务接口
 * 定义抽样结果推送相关的业务契约
 */
public interface SamplingResultPushService {

    /**
     * 推送抽样结果到指定级别
     * @param taskId 任务ID
     * @param targetLevel 目标级别
     * @return 推送结果
     */
    String pushSamplingResults(String taskId, String targetLevel);

    /**
     * 根据指定的地区ID推送抽样结果
     * @param taskId 任务ID
     * @param targetLevel 目标级别
     * @param zoneIds 地区ID列表（逗号分隔）
     * @return 推送结果
     */
    String pushSamplingResultsByIds(String taskId, String targetLevel, String zoneIds);

    /**
     * 推送数据到师级（师级机构可查看）
     * @param sampledData 抽样数据
     * @param btId 兵团ID
     */
    void pushToShiLevel(List<SamplingTaskReport> sampledData, String btId);

    /**
     * 推送数据到团级（团级机构可查看）
     * @param sampledData 抽样数据
     * @param shiId 师级ID
     */
    void pushToTuanLevel(List<SamplingTaskReport> sampledData, String shiId);

    /**
     * 推送数据到连级（连级机构可查看）
     * @param sampledData 抽样数据
     * @param tuanId 团级ID
     */
    void pushToLianLevel(List<SamplingTaskReport> sampledData, String tuanId);

    /**
     * 推送最终结果（预留居民管理模块接口）
     * @param finalData 最终数据
     * @param lianId 连级ID
     */
    void pushFinalResults(List<SamplingTaskReport> finalData, String lianId);

    /**
     * 根据任务ID查询抽样结果
     * @param taskId 任务ID
     * @return 抽样结果列表
     */
    List<SamplingResult> getResultsByTaskId(String taskId);

    /**
     * 根据机构权限查询可见的抽样结果
     * @param orgCode 机构代码
     * @param taskLevel 任务级别
     * @return 可见的抽样结果
     */
    List<SamplingResult> getVisibleResults(String orgCode, String taskLevel);

    /**
     * 标记结果为已确认
     * @param resultIds 结果ID列表
     * @param confirmBy 确认人
     */
    void confirmResults(List<String> resultIds, String confirmBy);

    /**
     * 删除指定任务的抽样结果
     * @param taskId 任务ID
     * @return 删除结果
     */
    String deleteResultsByTaskId(String taskId);

    /**
     * 查询抽样结果统计信息
     * @param taskId 任务ID
     * @return 统计信息
     */
    String getResultStatistics(String taskId);
}