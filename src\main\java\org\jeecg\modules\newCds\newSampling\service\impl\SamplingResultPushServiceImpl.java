package org.jeecg.modules.newCds.newSampling.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.newCds.newSampling.entity.SamplingResult;
import org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport;
import org.jeecg.modules.newCds.newSampling.mapper.SamplingResultMapper;
import org.jeecg.modules.newCds.newSampling.mapper.SamplingTaskReportMapper;
import org.jeecg.modules.newCds.newSampling.service.SamplingResultPushService;
import org.jeecg.modules.system.mapper.SysZoneCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽样结果推送服务实现类
 * 实现抽样数据推送到结果表中供各级机构查看的具体业务逻辑
 */
@Slf4j
@Service
public class SamplingResultPushServiceImpl implements SamplingResultPushService {

    @Autowired
    private SamplingResultMapper resultMapper;

    @Autowired
    private SamplingTaskReportMapper taskReportMapper;

    @Autowired
    private SysZoneCodeMapper sysZoneCodeMapper;

    /**
     * 推送抽样结果到指定级别
     * @param taskId 任务ID
     * @param targetLevel 目标级别
     * @return 推送结果
     */
    @Override
    @AutoLog(value = "抽样结果推送", operateType = 1)
    @Transactional(rollbackFor = Exception.class)
    public String pushSamplingResults(String taskId, String targetLevel) {
        try {
            log.info("开始推送抽样结果: taskId={}, targetLevel={}", taskId, targetLevel);

            // 1. 查询已上报的所有数据
            List<SamplingTaskReport> allData = taskReportMapper.selectSampledDataByTaskId(taskId);
            if (allData == null || allData.isEmpty()) {
                String msg = "未找到已上报的数据，任务ID: " + taskId;
                log.warn(msg);
                return msg;
            }

            log.info("找到已上报数据: {} 条", allData.size());

            // 2. 进行抽样，根据任务配置获取抽样数量
            int sampleCount = getSampleCountFromTaskConfig(taskId, targetLevel);
            List<SamplingTaskReport> sampledData = performSampling(allData, sampleCount, taskId);
            if (sampledData.isEmpty()) {
                String msg = "抽样结果为空，任务ID: " + taskId;
                log.warn(msg);
                return msg;
            }

            log.info("抽样完成: 从{}条数据中抽取了{}条", allData.size(), sampledData.size());
            
            // 2. 检查是否已经推送过
            QueryWrapper<SamplingResult> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("task_id", taskId).eq("report_level", targetLevel);
            int existCount = Math.toIntExact(resultMapper.selectCount(checkWrapper));
            if (existCount > 0) {
                String msg = "任务已推送过，跳过重复推送: taskId=" + taskId + ", targetLevel=" + targetLevel;
                log.warn(msg);
                return msg;
            }
            
            // 3. 转换并插入数据
            List<SamplingResult> results = new ArrayList<>();
            for (SamplingTaskReport data : sampledData) {
                SamplingResult result = createSamplingResult(data, taskId, targetLevel);
                if (result != null) {
                    results.add(result);
                } else {
                    log.error("创建结果对象失败，数据: {}", data);
                }
            }
            
            // 4. 批量插入数据
            if (!results.isEmpty()) {
                for (SamplingResult result : results) {
                    resultMapper.insert(result);
                }
                
                String msg = "抽样结果推送完成，任务ID: " + taskId + ", 数据量: " + results.size();
                log.info(msg);
                return msg;
            } else {
                String msg = "转换后的数据为空，任务ID: " + taskId;
                log.warn(msg);
                return msg;
            }
            
        } catch (Exception e) {
            String msg = "抽样结果推送失败，任务ID: " + taskId + ", 错误: " + e.getMessage();
            log.error(msg, e);
            throw new RuntimeException(msg);
        }
    }

    /**
     * 创建抽样结果对象
     */
    private SamplingResult createSamplingResult(SamplingTaskReport data, String taskId, String targetLevel) {
        try {
            SamplingResult result = new SamplingResult();

            // 基本信息
            result.setId(UUIDGenerator.generate());
            result.setTaskId(taskId);
            result.setOriginalTaskId(oConvertUtils.getString(data.getTaskId(), ""));

            // 地区信息
            result.setZoneCode(oConvertUtils.getString(data.getZoneCode()));
            result.setZoneName(oConvertUtils.getString(data.getZoneName()));
            result.setStreetCode(oConvertUtils.getString(data.getStreetCode()));
            result.setStreetName(oConvertUtils.getString(data.getStreetName()));
            result.setParentZone(oConvertUtils.getString(data.getParentZone()));
            result.setParentZoneName(oConvertUtils.getString(data.getParentZoneName()));

            // 人口信息
            result.setResidentPopulation(oConvertUtils.getString(data.getResidentPopulation()));

            // 监测点信息
            result.setMonitorPoint(oConvertUtils.getString(data.getMonitorPoint()));

            // 级别和类型
            result.setReportLevel(targetLevel);
            result.setResultType(getLevelDescription(targetLevel));

            // 状态设置
            result.setStatus("1"); // 有效状态
            result.setVisibleOrg(getVisibleOrgByLevel(targetLevel));
            result.setIsConfirmed("0"); // 未确认
            result.setPushStatus("1"); // 已推送
            result.setPushTime(new Date()); // 推送时间

            // 时间信息
            result.setCreateTime(new Date());
            result.setYears(oConvertUtils.getString(data.getYears(), "2025"));
            result.setCreateBy("system");

            return result;

        } catch (Exception e) {
            log.error("创建抽样结果对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取级别描述
     */
    private String getLevelDescription(String level) {
        switch (level) {
            case "2": return "师级抽样结果";
            case "3": return "团级抽样结果";
            case "4": return "连级抽样结果";
            case "5": return "最终抽样结果";
            default: return "抽样结果";
        }
    }

    /**
     * 根据级别获取可见机构
     */
    private String getVisibleOrgByLevel(String level) {
        // 这里可以根据实际业务需求设置可见机构
        // 暂时返回默认值
        return "ALL";
    }

    /**
     * 根据指定的地区ID推送抽样结果
     * @param taskId 任务ID
     * @param targetLevel 目标级别
     * @param zoneIds 地区ID列表（逗号分隔）
     * @return 推送结果
     */
    @Override
    @AutoLog(value = "根据地区ID推送抽样结果", operateType = 1)
    @Transactional(rollbackFor = Exception.class)
    public String pushSamplingResultsByIds(String taskId, String targetLevel, String zoneIds) {
        try {
            log.info("开始根据地区ID推送抽样结果: taskId={}, targetLevel={}, zoneIds={}", taskId, targetLevel, zoneIds);

            if (zoneIds == null || zoneIds.trim().isEmpty()) {
                String msg = "地区ID列表为空，任务ID: " + taskId;
                log.warn(msg);
                return msg;
            }

            // 1. 根据记录ID查询对应的上报数据
            String[] idArray = zoneIds.split(",");
            List<String> idList = new ArrayList<>();

            for (String id : idArray) {
                if (id != null && !id.trim().isEmpty()) {
                    idList.add(id.trim());
                }
            }

            List<SamplingTaskReport> selectedData = taskReportMapper.selectByIds(idList);

            if (selectedData.isEmpty()) {
                String msg = "根据地区ID未找到对应的上报数据，任务ID: " + taskId + ", 地区IDs: " + zoneIds;
                log.warn(msg);
                return msg;
            }

            log.info("根据地区ID找到上报数据: {} 条", selectedData.size());

            // 2. 检查是否已经推送过
            QueryWrapper<SamplingResult> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("task_id", taskId).eq("report_level", targetLevel);
            int existCount = Math.toIntExact(resultMapper.selectCount(checkWrapper));
            if (existCount > 0) {
                String msg = "任务已推送过，跳过重复推送: taskId=" + taskId + ", targetLevel=" + targetLevel;
                log.warn(msg);
                return msg;
            }

            // 3. 转换并插入数据（不再进行抽样，直接使用前端选中的数据）
            List<SamplingResult> results = new ArrayList<>();
            for (SamplingTaskReport data : selectedData) {
                SamplingResult result = createSamplingResult(data, taskId, targetLevel);
                if (result != null) {
                    results.add(result);
                } else {
                    log.error("创建结果对象失败，数据: {}", data);
                }
            }

            // 4. 批量插入数据
            if (!results.isEmpty()) {
                for (SamplingResult result : results) {
                    resultMapper.insert(result);
                }

                String msg = "根据地区ID推送抽样结果完成，任务ID: " + taskId + ", 数据量: " + results.size();
                log.info(msg);
                return msg;
            } else {
                String msg = "转换后的数据为空，任务ID: " + taskId;
                log.warn(msg);
                return msg;
            }

        } catch (Exception e) {
            String msg = "根据地区ID推送抽样结果失败，任务ID: " + taskId + ", 错误: " + e.getMessage();
            log.error(msg, e);
            throw new RuntimeException(msg);
        }
    }

    /**
     * 根据任务ID查询抽样结果
     * @param taskId 任务ID
     * @return 抽样结果列表
     */
    @Override
    public List<SamplingResult> getResultsByTaskId(String taskId) {
        try {
            List<SamplingResult> results = resultMapper.selectByTaskId(taskId);

            // 处理地区名称显示问题
            for (SamplingResult result : results) {
                // 如果地区名称为空或者是编码格式，通过编码查询真实名称
                if (result.getZoneName() == null || isCodeFormat(result.getZoneName())) {
                    String realZoneName = getZoneNameByCode(result.getZoneCode());
                    if (realZoneName != null) {
                        result.setZoneName(realZoneName);
                    }
                }

                // 如果街道名称为空或者是编码格式，通过编码查询真实名称
                if (result.getStreetName() == null || isCodeFormat(result.getStreetName())) {
                    String realStreetName = getZoneNameByCode(result.getStreetCode());
                    if (realStreetName != null) {
                        result.setStreetName(realStreetName);
                    }
                }
            }

            log.info("查询抽样结果成功: taskId={}, 结果数量={}", taskId, results.size());
            return results;
        } catch (Exception e) {
            log.error("根据任务ID查询抽样结果失败: taskId={}", taskId, e);
            throw new RuntimeException("查询结果失败: " + e.getMessage());
        }
    }

    /**
     * 标记结果为已确认
     * @param resultIds 结果ID列表
     * @param confirmBy 确认人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmResults(List<String> resultIds, String confirmBy) {
        try {
            if (resultIds == null || resultIds.isEmpty()) {
                log.warn("确认列表为空");
                return;
            }

            resultMapper.batchUpdateConfirmStatus(resultIds, confirmBy, new Date());
            log.info("抽样结果确认完成，确认数量: {}", resultIds.size());

        } catch (Exception e) {
            log.error("抽样结果确认失败", e);
            throw new RuntimeException("结果确认失败: " + e.getMessage());
        }
    }

    /**
     * 根据机构权限查询可见的抽样结果
     * @param orgCode 机构代码
     * @param taskLevel 任务级别
     * @return 抽样结果列表
     */
    @Override
    public List<SamplingResult> getVisibleResults(String orgCode, String taskLevel) {
        try {
            return resultMapper.selectVisibleResults(orgCode, taskLevel);
        } catch (Exception e) {
            log.error("查询可见抽样结果失败: orgCode={}, taskLevel={}", orgCode, taskLevel, e);
            throw new RuntimeException("查询结果失败: " + e.getMessage());
        }
    }



    /**
     * 根据地区编码查询地区名称
     * @param zoneCode 地区编码
     * @return 地区名称
     */
    private String getZoneNameByCode(String zoneCode) {
        if (zoneCode == null || zoneCode.isEmpty()) {
            return null;
        }

        try {
            // 使用SysZoneCodeMapper查询地区名称
            String zoneName = sysZoneCodeMapper.queryCnameByCode(zoneCode);
            if (zoneName != null && !zoneName.isEmpty()) {
                return zoneName;
            } else {
                // 如果查询不到，返回null让前端显示编码
                return null;
            }
        } catch (Exception e) {
            log.error("查询地区名称失败，编码：" + zoneCode + ", 错误：" + e.getMessage());
            return null;
        }
    }

    /**
     * 判断字符串是否为编码格式（纯数字）
     * @param str 待判断的字符串
     * @return true表示是编码格式
     */
    private boolean isCodeFormat(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("^\\d+$");
    }

    /**
     * 执行抽样逻辑
     * @param allData 所有数据
     * @param sampleCount 抽样数量
     * @param taskId 任务ID（用作随机种子）
     * @return 抽样结果
     */
    private List<SamplingTaskReport> performSampling(List<SamplingTaskReport> allData, int sampleCount, String taskId) {
        if (allData == null || allData.isEmpty()) {
            return new ArrayList<>();
        }

        // 确保抽样数量不超过总数量
        sampleCount = Math.min(sampleCount, allData.size());

        // 使用任务ID作为随机种子，确保相同任务的抽样结果一致
        long seed = taskId.hashCode() * 31L + allData.hashCode();
        Random random = new Random(seed);

        // 进行随机抽样
        List<SamplingTaskReport> sampledList = random.ints(0, allData.size())
                .distinct()
                .limit(sampleCount)
                .mapToObj(allData::get)
                .collect(Collectors.toList());

        log.info("抽样完成: 从{}条数据中抽取了{}条", allData.size(), sampledList.size());
        return sampledList;
    }

    /**
     * 根据任务配置获取抽样数量
     * @param taskId 任务ID
     * @param targetLevel 目标级别
     * @return 抽样数量
     */
    private int getSampleCountFromTaskConfig(String taskId, String targetLevel) {
        try {
            // 根据目标级别确定需要查询的任务配置
            // targetLevel: "1"=兵团级, "2"=师级, "3"=团级, "4"=连级

            // 默认抽样数量
            int defaultCount = 3;

            // 这里可以根据实际业务需求来确定抽样数量
            // 目前先使用一个简单的映射规则
            switch (targetLevel) {
                case "1": // 兵团级推送，通常抽样数量较少
                    return 2;
                case "2": // 师级推送
                    return 3;
                case "3": // 团级推送
                    return 3;
                case "4": // 连级推送
                    return 5;
                default:
                    return defaultCount;
            }

        } catch (Exception e) {
            log.warn("获取任务配置抽样数量失败，使用默认值3: {}", e.getMessage());
            return 3;
        }
    }

    /**
     * 推送数据到师级（师级机构可查看）
     * @param sampledData 抽样数据
     * @param btId 兵团ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushToShiLevel(List<SamplingTaskReport> sampledData, String btId) {
        try {
            List<SamplingResult> results = sampledData.stream().map(data ->
                createSamplingResult(data, btId, "2")
            ).collect(Collectors.toList());

            for (SamplingResult result : results) {
                resultMapper.insert(result);
            }
            log.info("师级抽样结果推送完成，数据量: {}", results.size());

        } catch (Exception e) {
            log.error("师级抽样结果推送失败", e);
            throw new RuntimeException("数据推送失败: " + e.getMessage());
        }
    }

    /**
     * 推送数据到团级（团级机构可查看）
     * @param sampledData 抽样数据
     * @param shiId 师级ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushToTuanLevel(List<SamplingTaskReport> sampledData, String shiId) {
        try {
            List<SamplingResult> results = sampledData.stream().map(data ->
                createSamplingResult(data, shiId, "3")
            ).collect(Collectors.toList());

            for (SamplingResult result : results) {
                resultMapper.insert(result);
            }
            log.info("团级抽样结果推送完成，数据量: {}", results.size());

        } catch (Exception e) {
            log.error("团级抽样结果推送失败", e);
            throw new RuntimeException("数据推送失败: " + e.getMessage());
        }
    }

    /**
     * 推送数据到连级（连级机构可查看）
     * @param sampledData 抽样数据
     * @param tuanId 团级ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushToLianLevel(List<SamplingTaskReport> sampledData, String tuanId) {
        try {
            List<SamplingResult> results = sampledData.stream().map(data ->
                createSamplingResult(data, tuanId, "4")
            ).collect(Collectors.toList());

            for (SamplingResult result : results) {
                resultMapper.insert(result);
            }
            log.info("连级抽样结果推送完成，数据量: {}", results.size());

        } catch (Exception e) {
            log.error("连级抽样结果推送失败", e);
            throw new RuntimeException("数据推送失败: " + e.getMessage());
        }
    }

    /**
     * 推送最终结果（预留居民管理模块接口）
     * @param finalData 最终数据
     * @param lianId 连级ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushFinalResults(List<SamplingTaskReport> finalData, String lianId) {
        try {
            // 1. 推送到最终结果表
            List<SamplingResult> finalResults = finalData.stream().map(data ->
                createSamplingResult(data, lianId, "5")
            ).collect(Collectors.toList());

            for (SamplingResult result : finalResults) {
                resultMapper.insert(result);
            }

            // 2. 预留居民管理模块接口
            // TODO: 当居民管理模块开发完成后，在此处调用推送接口
            // residentManagementService.pushSamplingResults(finalResults);

            log.info("最终抽样结果推送完成，数据量: {}", finalResults.size());

        } catch (Exception e) {
            log.error("最终抽样结果推送失败", e);
            throw new RuntimeException("数据推送失败: " + e.getMessage());
        }
    }

    /**
     * 删除指定任务的抽样结果
     * @param taskId 任务ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteResultsByTaskId(String taskId) {
        try {
            QueryWrapper<SamplingResult> wrapper = new QueryWrapper<>();
            wrapper.eq("task_id", taskId);
            int deleteCount = resultMapper.delete(wrapper);

            String result = String.format("删除抽样结果完成，删除数量: %d", deleteCount);
            log.info(result);
            return result;

        } catch (Exception e) {
            log.error("删除抽样结果失败: taskId={}", taskId, e);
            throw new RuntimeException("删除失败: " + e.getMessage());
        }
    }

    /**
     * 查询抽样结果统计信息
     * @param taskId 任务ID
     * @return 统计信息
     */
    @Override
    public String getResultStatistics(String taskId) {
        try {
            QueryWrapper<SamplingResult> wrapper = new QueryWrapper<>();
            wrapper.eq("task_id", taskId);
            int totalCount = Math.toIntExact(resultMapper.selectCount(wrapper));

            wrapper.eq("is_confirmed", "1");
            int confirmedCount = Math.toIntExact(resultMapper.selectCount(wrapper));

            return String.format("任务ID: %s, 总结果数: %d, 已确认数: %d, 确认率: %.2f%%",
                taskId, totalCount, confirmedCount,
                totalCount > 0 ? (confirmedCount * 100.0 / totalCount) : 0.0);

        } catch (Exception e) {
            log.error("查询抽样结果统计失败: taskId={}", taskId, e);
            return "统计查询失败";
        }
    }
}
