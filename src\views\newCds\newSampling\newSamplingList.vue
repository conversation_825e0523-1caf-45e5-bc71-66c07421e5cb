<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'progress'">
          <div class="progress-container">
            <!-- 进度条显示百分比 -->
            <div class="progress-wrapper">
              <a-progress
                :percent="getActualProgress(record)"
                :status="getProgressStatus(getActualProgress(record))"
                :stroke-color="getProgressColor(getActualProgress(record))"
                trail-color="#f0f0f0"
                :stroke-width="8"
                :show-info="true"
                :format="(percent) => `${percent}%`"
              />
            </div>
            <!-- 详细信息 -->
            <div class="progress-info">
              <span class="progress-text">
                已上报: <span class="text-blue-600 font-medium">{{ reportedCounts[record.id] || 0 }}</span> /
                需报: <span class="text-gray-600 font-medium">{{ getTargetCount(record) }}</span>
              </span>
            </div>
          </div>
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <newSamplingModal @register="registerModal" @success="handleSuccess"></newSamplingModal>
    <CommonModal @register="registerCommonModel" @success="handleSuccess" />
    <ZoneListModal @register="registerZoneListModal" @success="handleSuccess"></ZoneListModal>

    <DataReportModal @register="registerDataReportModal" @success="handleSuccess" />
    <SamplingResultModal @register="registerSamplingResultModal" />
  </div>
</template>

<script lang="ts" name="newSampling-newSampling" setup>
import { ref, reactive } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import newSamplingModal from './components/newSamplingModal.vue';

import DataReportModal from './components/DataReportModal.vue';
import SamplingResultModal from './components/SamplingResultModal.vue';
import { columns, searchFormSchema, superQuerySchema, formSchema } from './newSampling.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, save, getReportedCount } from './newSampling.api';
import { useUserStore } from '/@/store/modules/user';
import CommonModal from '../common/CommonModal.vue';
import dayjs from 'dayjs';
import ZoneListModal from './listModal/ZoneListModal.vue';

const queryParam = reactive<any>({});
const userStore = useUserStore();

// 存储每个任务的已上报数量
const reportedCounts = ref<Record<string, number>>({});
//注册model
const [registerModal, { openModal }] = useModal();
const [registerCommonModel, { openModal: openCommonModal }] = useModal();
const [registerZoneListModal, { openModal: openZoneListModal }] = useModal();

const [registerDataReportModal, { openModal: openDataReportModal }] = useModal();
const [registerSamplingResultModal, { openModal: openSamplingResultModal }] = useModal();
//注册table数据
const { tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '慢病抽样任务',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToNumber: [
      ],
      fieldMapToTime: [
      ],
    },
    actionColumn: {
      width: 240,
      fixed: 'right'
    },
    beforeFetch: (params) => {
      return Object.assign(params, queryParam);
    },
    afterFetch: (data) => {
      // 加载已上报数量
      loadReportedCounts(data);
      return data;
    },
  },
  exportConfig: {
    name: "慢病抽样任务",
    url: getExportUrl,
    params: queryParam,
  },
  importConfig: {
    url: getImportUrl,
    success: handleSuccess
  },
})

const [registerTable, { reload }, { selectedRowKeys }] = tableContext

// 高级查询配置
const superQueryConfig = reactive(superQuerySchema);
/**
   * 操作栏
   */
function getTableAction(record) {
  const arr: any[] = [];

  // 获取已上报数量和目标数量
  const reportedCount = reportedCounts.value[record.id] || 0;
  const targetCount = getTargetCount(record);

  // 判断是否可以抽样
  const canSample = reportedCount >= targetCount && targetCount > 0;

  // 根据状态和条件显示对应按钮
  if (record.status == '5') {
    // 待抽取状态，显示确认抽取按钮
    arr.push({
      label: '确认抽取',
      icon: 'ant-design:check-circle-outlined',
      type: 'primary',
      size: 'small',
      onClick: handleZoneSampling.bind(null, record),
    });
  } else if (canSample && (record.status == '2' || record.status == '3' || record.status == '4')) {
    // 等待上报状态但已达到目标数量，显示抽样按钮
    arr.push({
      label: '开始抽样',
      icon: 'ant-design:thunderbolt-outlined',
      type: 'primary',
      size: 'small',
      onClick: handleZoneSampling.bind(null, record),
    });
  } else if (!canSample && targetCount > 0) {
    // 上报数量不足，显示无法抽样提示
    arr.push({
      label: `无法抽样(${reportedCount}/${targetCount})`,
      icon: 'ant-design:exclamation-circle-outlined',
      type: 'default',
      size: 'small',
      disabled: true,
    });
  }

  // 查看抽样结果按钮 - 已完成状态时显示
  if (record.status == '6') {
    arr.push({
      label: '查看结果',
      icon: 'ant-design:eye-outlined',
      type: 'default',
      size: 'small',
      onClick: handleViewResults.bind(null, record),
    });
  }

  // 数据上报按钮 - 始终显示
  arr.push({
    label: '数据上报',
    icon: 'ant-design:upload-outlined',
    type: 'primary',
    size: 'small',
    onClick: handleDataReport.bind(null, record),
  });

  return arr;
}

/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '详情',
      icon: 'ant-design:eye-outlined',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '编辑',
      icon: 'ant-design:edit-outlined',
      onClick: handleEdit.bind(null, record),
    },
    {
      label: '删除',
      icon: 'ant-design:delete-outlined',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
        placement: 'topLeft',
      }
    }
  ];
}

/**
 * 查看抽样结果
 */
function handleViewResults(record) {
  openSamplingResultModal(true, {
    ...record,
    level: 'bt' // 兵团级
  });
}

/**
 * 兵团确认抽样
 */
function handleZoneSampling(record) {
  openZoneListModal(true, {
    btId: record.id,
    years: record.years,
    name: record.name,
    shiNum: record.shiNum,
    shiSampleNum: record.shiSampleNum,
    tuanNum: record.tuanNum,
    tuanSampleNum: record.tuanSampleNum,
    lianNum: record.lianNum,
    lianSampleNum: record.lianSampleNum,
    layerNum: record.layerNum,
    totalNum: record.totalNum,
    sampleNum: record.shiSampleNum,
    needPopulation: record.totalNum,
    sampleZone: record.sampleZone,
    sampleZoneName: record.sampleZone_dictText
  })
}
/**
 * 高级查询事件
 */
function handleSuperQuery(params) {
  Object.keys(params).map((k) => {
    queryParam[k] = params[k];
  });
  reload();
}


/**
 * 新增事件
 */
function handleAdd() {
  openCommonModal(true, {
    title: '新建抽样任务',
    formSchema: formSchema,
    record: {
      sampleZone: (userStore.getUserInfo as any).zonecode,
      samplingOrg: (userStore.getUserInfo as any).orgcode,
      years: dayjs().format('YYYY'),
    },
    api: save
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 详情
*/
function handleDetail(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, handleSuccess);
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
}
/**
 * 成功回调
 */
function handleSuccess() {
  selectedRowKeys.value = [];
  reload(); // 刷新主表
}


/**
 * 数据上报
 */
function handleDataReport(record) {
  openDataReportModal(true, {
    taskId: record.id,
    name: record.name,
    years: record.years,
    sampleNum: record.layerNum,
    // 传递任务地区信息，用于自动填充上级地区名称
    taskZoneName: record.sampleZone_dictText, // 任务地区名称
    taskZoneCode: record.sampleZone // 任务地区编码
  })
}

/**
 * 根据任务级别和层级计算目标数量
 * 使用映射表优化多重if判断
 */
function getTargetCount(record) {
  const { layerNum, taskLevel, shiNum, tuanNum, lianNum, sampleNum, totalNum } = record;

  // 定义层级和任务级别的映射关系
  const targetMapping = {
    '3': { // 三层：兵团->师->团->连
      '1': shiNum,   // 兵团级任务，显示师级数量
      '2': tuanNum,  // 师级任务，显示团级数量
      '3': lianNum,  // 团级任务，显示连级数量
      '4': sampleNum // 连级任务，显示需报人数
    },
    '2': { // 两层：师->团->连
      '2': tuanNum,  // 师级任务，显示团级数量
      '3': lianNum,  // 团级任务，显示连级数量
      '4': sampleNum // 连级任务，显示需报人数
    },
    '1': { // 一层：团->连
      '3': lianNum,  // 团级任务，显示连级数量
      '4': sampleNum // 连级任务，显示需报人数
    }
  };

  // 通过映射表获取目标数量，如果没有匹配则返回总计人数
  return targetMapping[layerNum]?.[taskLevel] || totalNum || 0;
}

/**
 * 加载已上报数量
 */
async function loadReportedCounts(records) {
  for (const record of records) {
    try {
      const count = await getReportedCount(record.id);
      reportedCounts.value[record.id] = count;
    } catch {
      reportedCounts.value[record.id] = 0;
    }
  }
}

/**
 * 获取进度条颜色
 */
function getProgressColor(percent) {
  if (percent >= 100) return '#52c41a'; // 绿色 - 完成
  if (percent >= 80) return '#1890ff'; // 蓝色 - 接近完成
  if (percent >= 50) return '#faad14'; // 橙色 - 进行中
  if (percent >= 20) return '#fa8c16'; // 深橙色 - 刚开始
  return '#ff4d4f'; // 红色 - 未开始或很少
}

/**
 * 获取进度条状态
 */
function getProgressStatus(percent) {
  if (percent >= 100) return 'success';
  if (percent >= 80) return 'active';
  if (percent >= 20) return 'active';
  return 'exception';
}



/**
 * 计算实际进度百分比
 */
function getActualProgress(record) {
  const reported = reportedCounts.value[record.id] || 0;
  const target = getTargetCount(record);

  if (target === 0) return 0;

  const progress = Math.round((reported / target) * 100);
  return Math.min(progress, 100); // 确保不超过100%
}

</script>

<style scoped>
:deep(.ant-picker),
:deep(.ant-input-number) {
  width: 100%;
}

/* 进度条容器样式 */
.progress-container {
  min-width: 280px;
  padding: 8px 0;
}

.progress-wrapper {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 12px;
  margin-top: 4px;
}

.progress-text {
  color: #666;
  flex: 1;
}

/* 进度条颜色样式 */
:deep(.ant-progress-bg) {
  transition: all 0.3s ease;
}

:deep(.ant-progress-text) {
  font-weight: 600;
  font-size: 12px;
}

/* 优化操作按钮样式 */
:deep(.ant-table-tbody .ant-table-cell) {
  padding: 8px 12px;
}

:deep(.table-action) {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  max-width: 100%;
}

:deep(.table-action .ant-btn) {
  margin-right: 0;
  font-size: 11px;
  padding: 2px 8px;
  height: 24px;
  line-height: 20px;
  min-width: auto;
  border-radius: 4px;
  flex-shrink: 1;
  font-weight: 400;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.table-action .ant-btn:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

:deep(.table-action .ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

:deep(.table-action .ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

:deep(.table-action .ant-btn-sm) {
  font-size: 11px;
  padding: 2px 6px;
  height: 22px;
  line-height: 18px;
}

/* 操作列样式优化 */
:deep(.ant-table-cell[data-index="action"]) {
  padding: 6px 8px !important;
  overflow: visible;
  width: 180px;
  min-width: 180px;
  max-width: 180px;
}

/* 下拉菜单按钮样式 */
:deep(.basic-table-action .ant-btn-link) {
  padding: 2px 6px;
  height: 24px;
  font-size: 11px;
  border-radius: 4px;
  color: #1890ff;
}

:deep(.basic-table-action .ant-btn-link:hover) {
  color: #40a9ff;
  background-color: rgba(24, 144, 255, 0.1);
}

/* 下拉菜单样式 */
:deep(.ant-dropdown-menu-item) {
  font-size: 12px;
  padding: 6px 12px;
}

:deep(.ant-dropdown-menu-item-icon) {
  margin-right: 6px;
}

/* 任务进度列样式 */
:deep(.ant-table-cell[data-index="progress"]) {
  padding: 12px 16px !important;
}

/* 颜色工具类 */
.text-blue-600 {
  color: #1890ff;
}

.text-gray-600 {
  color: #666;
}

.text-green-600 {
  color: #52c41a;
}

.text-orange-500 {
  color: #faad14;
}

.text-red-500 {
  color: #ff4d4f;
}

.font-medium {
  font-weight: 500;
}
</style>