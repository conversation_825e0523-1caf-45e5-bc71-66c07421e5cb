<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="modalTitle"
    :width="1200"
    :defaultFullscreen="true"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="p-4">
      <!-- 任务信息显示 -->
      <div class="mb-4 p-3 bg-gray-50 rounded">
        <h4 class="mb-2 font-semibold">任务信息</h4>
        <div class="grid grid-cols-2 gap-4">
          <div>任务名称：{{ taskInfo.name }}</div>
          <div>年度：{{ taskInfo.years }}</div>
          <div>需报数量：{{ getTaskTargetCount() }}</div>
          <div>已报数量：{{ reportedCount }}</div>
        </div>
      </div>

      <!-- 数据上报表格 -->
      <BasicTable
        @register="registerTable"
      >
        <template #toolbar>
          <a-button type="primary" @click="handleAdd">
            <Icon icon="ant-design:plus-outlined" />
            新增上报数据
          </a-button>
        </template>

        <!-- 状态显示插槽 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'reportStatus_dictText'">
            <div class="status-cell">
              <a-tag v-if="record.reportStatus === '2'" color="success">
                已上报
              </a-tag>
              <a-tag v-else-if="record.reportStatus === '1'" color="warning">
                草稿
              </a-tag>
              <a-tag v-else color="default">
                未知状态
              </a-tag>
            </div>
          </template>
        </template>

        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                tooltip: '编辑',
                onClick: handleEdit.bind(null, record),
                ifShow: true // 允许编辑所有状态的数据
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                tooltip: '移除',
                popConfirm: {
                  title: '是否确认移除此条数据？',
                  confirm: handleDelete.bind(null, record),
                },
                ifShow: true // 允许删除所有状态的数据
              }
            ]"
          />
        </template>
      </BasicTable>
    </div>

    <!-- 数据录入表单 -->
    <BasicModal
      @register="registerFormModal"
      title="数据上报"
      width="600"
      @ok="handleFormSubmit"
    >
      <BasicForm @register="registerForm" />
    </BasicModal>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { BasicForm, useForm } from '/@/components/Form';
import { reportColumns, reportFormSchema } from '../newSampling.data';
import { reportData, getReportList, deleteReport, updateProgress } from '../newSampling.api';
import { useMessage } from '/@/hooks/web/useMessage';
import Icon from '/@/components/Icon';

const emit = defineEmits(['success', 'register']);
const { createMessage } = useMessage();

// 任务信息
const taskInfo = ref<any>({});
const reportedCount = ref(0);

// 模态框标题
const modalTitle = computed(() => {
  return `数据上报 - ${taskInfo.value.name || ''}`;
});

// 主模态框
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  taskInfo.value = data;
  await reload();
});

// 表单模态框
const [registerFormModal, { openModal: openFormModal, closeModal: closeFormModal }] = useModal();

// 待设置的表单数据
const pendingFormData = ref<any>(null);

// 表单初始化状态
const formInitialized = ref(false);

// 表单
const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  labelWidth: 100,
  schemas: reportFormSchema,
  showActionButtonGroup: false,
});

// 增强的重置函数
const enhancedResetFields = () => {
  resetFields();
  formInitialized.value = false;
  pendingFormData.value = null;
};

// 表格
const [registerTable, { reload }] = useTable({
  title: '上报数据列表',
  api: getReportList,
  columns: reportColumns,
  formConfig: {
    labelWidth: 120,
  },
  pagination: true,
  striped: false,
  useSearchForm: false,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  canResize: false,
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
  beforeFetch: (params) => {
    return {
      ...params,
      taskId: taskInfo.value.taskId || taskInfo.value.id
    };
  },
  afterFetch: (data) => {
    // 统计已上报数据量
    reportedCount.value = data.filter(item => item.reportStatus === '2').length;
    return data;
  }
});

// 移除行选择功能（不需要批量操作）

// 获取监测点信息的通用函数
const getMonitorPointInfo = async () => {
  try {
    const { monirotList } = await import('../../monitor/BtptCdsMonitorPoint.api');
    const currentYear = taskInfo.value.years || new Date().getFullYear();

    const monitorResult = await monirotList({
      years: currentYear,
      pageSize: 50
    });

    if (monitorResult && monitorResult.records && monitorResult.records.length > 0) {
      return monitorResult.records[0].name;
    }
    return '';
  } catch {
    return '';
  }
};

// 专门处理 Zonecode 组件重新初始化的函数
const reinitializeZonecodeComponents = async () => {
  try {
    // 等待表单完全渲染
    await new Promise(resolve => setTimeout(resolve, 800));
  } catch {
    // 静默处理错误
  }
};

// 安全设置表单字段值的函数
const safeSetFieldsValue = async (values: any) => {
  try {
    // 等待DOM更新完成
    await nextTick();

    // 等待表单完全初始化
    await new Promise(resolve => setTimeout(resolve, 500));

    // 直接设置所有字段值
    setFieldsValue(values);
    formInitialized.value = true;

  } catch {
    // 备用方案：再次尝试设置
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      setFieldsValue(values);
      formInitialized.value = true;
    } catch {
      // 静默处理错误
    }
  }
};

// 新增数据
const handleAdd = async () => {
  // 先打开模态框
  openFormModal(true, {});

  // 等待模态框完全打开
  await new Promise(resolve => setTimeout(resolve, 100));

  // 重置表单
  enhancedResetFields();

  // 获取任务地区名称作为上报地区
  const parentZoneName = taskInfo.value.taskZoneName || taskInfo.value.sampleZone_dictText || '';

  // 获取监测点信息
  const monitorPointName = await getMonitorPointInfo();

  // 处理年份字段，确保只保存年份部分
  let yearValue = taskInfo.value.years;
  if (yearValue) {
    // 如果是日期格式，提取年份
    if (yearValue.includes('-') || yearValue.includes('/')) {
      yearValue = new Date(yearValue).getFullYear().toString();
    }
    // 如果是完整日期时间格式，也提取年份
    else if (yearValue.length > 4) {
      yearValue = yearValue.substring(0, 4);
    }
  }

  const formData = {
    taskId: taskInfo.value.taskId || taskInfo.value.id,
    years: yearValue, // 设置年份（只保存年份部分）
    parentZoneName: parentZoneName, // 设置上报地区为任务地区
    monitorPoint: monitorPointName // 设置监测点
  };



  // 先重新初始化 Zonecode 组件
  await reinitializeZonecodeComponents();

  // 使用安全设置函数
  await safeSetFieldsValue(formData);
};

// 编辑数据
const handleEdit = async (record: any) => {

  // 先打开模态框
  openFormModal(true, record);

  // 等待模态框完全打开
  await new Promise(resolve => setTimeout(resolve, 100));

  // 重置表单
  enhancedResetFields();

  // 如果记录中没有上报地区或监测点，则自动获取
  let parentZoneName = record.parentZoneName;
  let monitorPointName = record.monitorPoint;

  // 如果没有上报地区，使用任务地区
  if (!parentZoneName) {
    parentZoneName = taskInfo.value.taskZoneName || taskInfo.value.sampleZone_dictText || '';
  }

  // 如果没有监测点，尝试获取
  if (!monitorPointName) {
    monitorPointName = await getMonitorPointInfo();
  }

  // 处理年份字段，确保只保存年份部分
  let yearValue = record.years;
  if (yearValue) {
    // 如果是日期格式，提取年份
    if (yearValue.includes('-') || yearValue.includes('/')) {
      yearValue = new Date(yearValue).getFullYear().toString();
    }
    // 如果是完整日期时间格式，也提取年份
    else if (yearValue.length > 4) {
      yearValue = yearValue.substring(0, 4);
    }
  }

  // 设置表单值，确保包含ID和其他必要字段
  const formData = {
    ...record,
    id: record.id, // 确保ID被正确传递
    years: yearValue, // 确保年份只保存年份部分
    parentZoneName: parentZoneName,
    monitorPoint: monitorPointName,
    taskId: record.taskId || taskInfo.value.taskId || taskInfo.value.id
  };



  // 先重新初始化 Zonecode 组件
  await reinitializeZonecodeComponents();

  // 使用安全设置函数
  await safeSetFieldsValue(formData);
};

// 删除数据
const handleDelete = async (record: any) => {
  try {
    await deleteReport(record.id);
    // 不显示删除成功消息，因为后端已经返回了成功消息
    await reload();
    // 更新任务进度
    await updateProgress(taskInfo.value.taskId || taskInfo.value.id);
  } catch (error) {
    createMessage.error('删除失败');
  }
};



// 表单提交
const handleFormSubmit = async () => {
  let values: any = {};
  try {
    values = await validate();

    // 处理地区编码和名称：提取最后一级编码
    const fieldsToProcess = ['zoneCode', 'zoneName', 'streetCode', 'streetName'];
    fieldsToProcess.forEach(field => {
      if (values[field] && values[field].includes(',')) {
        values[field] = values[field].split(',').pop();
      }
    });

    // 确保设置正确的状态和任务信息
    values.years = taskInfo.value.years; // 设置年份
    values.reportStatus = '2'; // 强制设置为已上报状态，确保点击确定后直接为已上报

    // 确保任务ID正确设置
    if (!values.taskId) {
      values.taskId = taskInfo.value.taskId || taskInfo.value.id;
    }

    // 检查是否是编辑模式（如果有id且不为空则是编辑）
    const isEdit = values.id && values.id.trim() !== '';

    await reportData(values);
    createMessage.success(isEdit ? '数据更新成功' : '数据上报成功');
    closeFormModal();
    await reload();
    // 更新任务进度
    await updateProgress(taskInfo.value.taskId || taskInfo.value.id);
  } catch {
    const isEditMode = values.id && values.id.trim() !== '';
    createMessage.error(isEditMode ? '更新失败' : '上报失败');
  }
};

// 移除批量提交功能

// 主模态框提交
const handleSubmit = async () => {
  closeModal();
  emit('success');
};

// 主模态框取消
const handleCancel = () => {
  closeModal();
};



// 计算任务目标数量
const getTaskTargetCount = () => {
  const { layerNum, taskLevel, shiNum, tuanNum, lianNum, sampleNum } = taskInfo.value;

  // 层级和任务级别对应的目标数量映射表
  const targetCountMap = {
    '3': { // 三层：兵团->师->团->连
      '1': shiNum,   // 兵团级任务，显示师级数量
      '2': tuanNum,  // 师级任务，显示团级数量
      '3': lianNum,  // 团级任务，显示连级数量
      '4': sampleNum // 连级任务，显示需报人数
    },
    '2': { // 两层：师->团->连
      '2': tuanNum,  // 师级任务，显示团级数量
      '3': lianNum,  // 团级任务，显示连级数量
      '4': sampleNum // 连级任务，显示需报人数
    },
    '1': { // 一层：团->连
      '3': lianNum,  // 团级任务，显示连级数量
      '4': sampleNum // 连级任务，显示需报人数
    }
  };

  // 根据层级和任务级别获取对应的目标数量，如果没有匹配则返回默认值
  return targetCountMap[layerNum]?.[taskLevel] || sampleNum || 0;
};
</script>

<style scoped>
/* 布局相关 */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

/* 间距相关 */
.gap-4 {
  gap: 1rem;
}

.gap-2 {
  gap: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

/* 外观相关 */
.bg-gray-50 {
  background-color: #f9fafb;
}

.rounded {
  border-radius: 0.25rem;
}

.font-semibold {
  font-weight: 600;
}

/* 组件特定样式 */
.status-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
