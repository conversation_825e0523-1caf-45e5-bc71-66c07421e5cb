import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/collectioncode/cdsCollectionCode/list',
  save='/collectioncode/cdsCollectionCode/add',
  edit='/collectioncode/cdsCollectionCode/edit',
  deleteOne = '/collectioncode/cdsCollectionCode/delete',
  deleteBatch = '/collectioncode/cdsCollectionCode/deleteBatch',
  importExcel = '/collectioncode/cdsCollectionCode/importExcel',
  exportXls = '/collectioncode/cdsCollectionCode/exportXls',
  findByWzxyz = '/collectioncode/cdsCollectionCode/findByWzxyz',
  queryById = '/collectioncode/cdsCollectionCode/queryById',
}
export const findByIdDetail = (params,handleDetail)=>{
  return defHttp.get({url: Api.queryById, params}, {joinParamsToUrl: true}).then((res)=>{
    handleDetail(res);
  })
}
export const findByIdEdit = (params,handleEdit)=>{
  return defHttp.get({url: Api.queryById, params}, {joinParamsToUrl: true}).then((res)=>{
    handleEdit(res);
  })
}

export const findByWzxyz = (params,handleWzxyzs) => {
  return defHttp.get({url: Api.findByWzxyz, params}, {joinParamsToUrl: true}).then((res) => {
    handleWzxyzs(res);
  });
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
