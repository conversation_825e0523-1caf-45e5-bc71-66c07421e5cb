<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.newCds.newSampling.mapper.NewSamplingMapper">

    <select id="queryShi" resultType="org.jeecg.modules.newCds.newSampling.entity.vo.ZoneVo"
            parameterType="java.lang.String">
        select
        b.id,
        a.popular,
        b.code,
        b.full_name
        from btpt_cds_popular a left join dd_zone b on a.code = b.code
        where 1=1
        <if test="removeIds !=null and removeIds !=''">
            and b.id not in
            <foreach item="removeIds" collection="removeIds.split(',')" open="(" separator="," close=")">
                #{removeIds}
            </foreach>
        </if>
        ORDER BY a.code asc
    </select>
    <select id="selectZoneByIds" resultType="org.jeecg.modules.newCds.newSampling.entity.vo.ZoneVo"
            parameterType="java.lang.String">
        select
            b.id,
            a.popular,
            b.code,
            b.full_name
        from btpt_cds_popular a left join dd_zone b on a.code = b.code
        where
            b.id in
        <foreach item="ids" collection="ids.split(',')" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>
</mapper>