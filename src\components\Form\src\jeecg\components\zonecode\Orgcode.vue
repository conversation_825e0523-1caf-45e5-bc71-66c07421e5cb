<template>
  <a-select v-bind="attrs"  :value="cascaderValue" :options="getOptions"   @change="onChange" @search="onSearch" />
</template>

<script lang="ts">
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { defineComponent, ref, watch, computed ,watchEffect,inject} from 'vue';
   import { defHttp } from '/@/utils/http/axios';
   enum Api {
     getData = '/sys/organise/qryOrganise' 
   }
  //用户信息
 import {useUserStore} from '/@/store/modules/user'
  // 可以输入的下拉框（此组件暂时没有人用）
  export default defineComponent({
    name: 'Orgcode',
    props: {
      value: propTypes.oneOfType([propTypes.object, propTypes.array, propTypes.string]),
        /**
         * 多个类型使用逗号隔开，例如医院和疾控，A,J
         *  A	医院
            B	社区卫生服务中心（站）   
            C	卫生院
            D	门诊部、诊所、医务室、村卫生室
            E	急救中心（站）
            F	采供血机构
            G	妇幼保健院(所、站)      
            H	专科疾病防治院(所、站)
            J	疾病预防控制中心(防疫站)
            K	卫生监督所(局)
            L	卫生监督检验(监测、检测)所(站)
            M	医学科学研究机构
            N	医学教育机构
            O	健康教育所(站、中心)       
            P	其他卫生机构
            Q	卫生社会团体
            X	卫生行政机构
         *  */
      orgType: propTypes.string.def(''),
      zonecode:propTypes.string.def('')
    },
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
       const userInfo = useUserStore().getUserInfo //用户信息
      // 内部 options 选项
      const attrs = useAttrs();
       const getOptions =  ref<any[]>([]);
       const cascaderValue = ref([]);
      // 监听外部 zonecode 变化，并覆盖内部 options
      //接收下拉框选项
     // const zonecode = inject('zonecode') || ref([]);
      watch(
        () => props.zonecode,
        () => loadRoot(),
        { deep: true, immediate: true }
      );
      
      const ifHandChangd = ref(false);
      async function loadRoot() {
       const params = { zonecode:props.zonecode||userInfo.zonecode,
              orgType:props.orgType  
             }    
        let result = await defHttp.post({ url: Api.getData, params });
        getOptions.value = [...result];
        console.log("清空orgcode")
         if(props.value){
          cascaderValue.value= props.value
        }
        if(ifHandChangd.value){
          cascaderValue.value=undefined;
        }
      }
      
      function onChange(...args: any[]) {
        cascaderValue.value = args[0]
        //deleteSearchAdd(args[0]);
        ifHandChangd.value=true
        emit('change', ...args);
        emit('update:value', args[0]);
      }

      function onSearch(value) {
        // 是否找到了对应的项，找不到则添加这一项
        let foundIt =
          getOptions.value.findIndex((option) => {
            return option.value.toString() === value.toString();
          }) !== -1;
        // !!value ：不添加空值
        if (!foundIt && !!value) {
          deleteSearchAdd(value);
          // searchAdd 是否是通过搜索添加的
          getOptions.value.push({ value: value, searchAdd: true });
          //onChange(value,{ value })
        } else if (foundIt) {
          onChange(value);
        }
      }

      // 删除无用的因搜索（用户输入）而创建的项
      function deleteSearchAdd(value = '') {
        let indexes: any[] = [];
        getOptions.value.forEach((option, index) => {
          if (option.searchAdd) {
            if ((option.value ?? '').toString() !== value.toString()) {
              indexes.push(index);
            }
          }
        });
        // 翻转删除数组中的项
        for (let index of indexes.reverse()) {
          getOptions.value.splice(index, 1);
        }
      }

      return { 
        attrs,
        cascaderValue,
        getOptions, 
        onChange,
        onSearch,
      };
    },
  });
</script>

<style scoped></style>
