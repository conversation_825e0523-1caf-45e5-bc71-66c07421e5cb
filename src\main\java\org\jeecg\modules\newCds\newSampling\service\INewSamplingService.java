package org.jeecg.modules.newCds.newSampling.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.newCds.newSampling.child.entity.Popular;
import org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport;
import org.jeecg.modules.newCds.newSampling.entity.dto.SampleDto;
import org.jeecg.modules.newCds.newSampling.entity.pojo.NewSampling;
import org.jeecg.modules.newCds.newSampling.entity.vo.ZoneVo;
import org.jeecg.modules.newCds.newSampling.entity.vo.SamplingReportVO;

import java.util.List;

/**
 * @Description: 慢病抽样任务
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
public interface INewSamplingService extends IService<NewSampling> {

    //兵团确认抽样
    String btSampleShi(SampleDto dto);

    //师级确认抽样
    String shiSampleTuan(SampleDto dto);

    //团级确认抽样
    String tuanSampleLian(SampleDto dto);

    //删除所有任务
    void removeAllTask(String id);

    //地区上报人数分页
    List<Popular> queryZoneList(SampleDto dto);

    //地区上报数据分页 - 新方法，返回专门的VO
    List<SamplingReportVO> queryZoneReportList(SampleDto dto);

    //更新任务进度
    void updateTaskProgress(String taskId);

    /**
     * 创建新的抽样任务（包含业务逻辑处理）
     * @param newSampling 任务实体
     * @param user 当前用户
     * @return 创建结果
     */
    String createSamplingTask(NewSampling newSampling, LoginUser user);

    /**
     * 数据上报业务处理
     * @param samplingTaskReport 上报数据
     * @param user 当前用户
     * @return 处理结果
     */
    String reportDataBusiness(SamplingTaskReport samplingTaskReport, LoginUser user);

    /**
     * 获取上报数据列表（带分页）
     * @param taskId 任务ID
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    IPage<SamplingReportVO> getReportListWithPagination(String taskId, Integer pageNo, Integer pageSize);

    /**
     * 生成数据上报模板
     * @param isTemplate 是否为新模板格式
     * @return ModelAndView对象
     */
    org.springframework.web.servlet.ModelAndView generateReportTemplate(boolean isTemplate);
}
