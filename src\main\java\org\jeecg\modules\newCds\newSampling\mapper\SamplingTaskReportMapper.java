package org.jeecg.modules.newCds.newSampling.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport;

/**
 * @Description: 抽样任务上报数据
 * @Author: jeecg-boot
 * @Date: 2025-07-22
 * @Version: V1.0
 */
@Mapper
public interface SamplingTaskReportMapper extends BaseMapper<SamplingTaskReport> {

    /**
     * 根据任务ID查询已上报的抽样数据
     * @param taskId 任务ID
     * @return 抽样数据列表
     */
    java.util.List<SamplingTaskReport> selectSampledDataByTaskId(@Param("taskId") String taskId);

    /**
     * 根据任务ID和地区ID查询上报数据
     * @param taskId 任务ID
     * @param zoneId 地区ID
     * @return 抽样数据列表
     */
    java.util.List<SamplingTaskReport> selectByTaskIdAndZoneId(@Param("taskId") String taskId, @Param("zoneId") String zoneId);

    /**
     * 根据任务ID统计上报数据量
     * @param taskId 任务ID
     * @return 上报数据量
     */
    int countReportedDataByTaskId(@Param("taskId") String taskId);

    /**
     * 根据任务ID统计总人口数
     * @param taskId 任务ID
     * @return 总人口数
     */
    int sumPopulationByTaskId(@Param("taskId") String taskId);

    /**
     * 根据多个任务ID统计上报数据量
     * @param taskIds 任务ID列表
     * @return 上报数据量
     */
    int countReportedDataByTaskIds(@Param("taskIds") java.util.List<String> taskIds);

    /**
     * 根据多个任务ID统计总人口数
     * @param taskIds 任务ID列表
     * @return 总人口数
     */
    int sumPopulationByTaskIds(@Param("taskIds") java.util.List<String> taskIds);
}
