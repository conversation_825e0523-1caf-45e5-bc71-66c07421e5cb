<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.newCds.newSampling.mapper.SamplingTaskReportMapper">

    <!-- 根据任务ID查询已上报的抽样数据 -->
    <select id="selectSampledDataByTaskId" resultType="org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport">
        SELECT * FROM btpt_cds_sampling_task_report
        WHERE task_id = #{taskId}
        AND report_status = '2'
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务ID和地区ID查询上报数据 -->
    <select id="selectByTaskIdAndZoneId" resultType="org.jeecg.modules.newCds.newSampling.entity.SamplingTaskReport">
        SELECT * FROM btpt_cds_sampling_task_report
        WHERE task_id = #{taskId}
        AND zone_id = #{zoneId}
        AND report_status = '2'
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务ID统计上报数据量 -->
    <select id="countReportedDataByTaskId" resultType="int">
        SELECT COUNT(*) FROM btpt_cds_sampling_task_report 
        WHERE task_id = #{taskId} 
        AND report_status = '2'
    </select>

    <!-- 根据任务ID统计总人口数 -->
    <select id="sumPopulationByTaskId" resultType="int">
        SELECT COALESCE(SUM(CAST(resident_population AS INTEGER)), 0) 
        FROM btpt_cds_sampling_task_report 
        WHERE task_id = #{taskId} 
        AND report_status = '2'
    </select>

    <!-- 根据多个任务ID统计上报数据量 -->
    <select id="countReportedDataByTaskIds" resultType="int">
        SELECT COUNT(*) FROM btpt_cds_sampling_task_report 
        WHERE task_id IN 
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND report_status = '2'
    </select>

    <!-- 根据多个任务ID统计总人口数 -->
    <select id="sumPopulationByTaskIds" resultType="int">
        SELECT COALESCE(SUM(CAST(resident_population AS INTEGER)), 0) 
        FROM btpt_cds_sampling_task_report 
        WHERE task_id IN 
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND report_status = '2'
    </select>

</mapper>
