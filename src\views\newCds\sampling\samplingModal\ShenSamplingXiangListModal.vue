<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :width="800" :defaultFullscreen="true"
    :showFooter="false" :footer="null" :maskClosable="false">
    <!--引用表格-->
    <!-- :rowSelection="rowSelection" -->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <div class="stats-row">
          <span>合计人口数: {{ totalPopulation }}</span>
          <span style="margin-left: 50px;margin-right: 0">平均人口数: {{ averagePopulation }}</span>
        </div>
        <!-- <span>监测区划:{{ props.monitorZonecode }}</span>
        <span>监测点：{{ monitorPoint }}</span> -->
        <input v-model="sampleNum" placeholder="随机数因子，默认为3"
          style="width: 200px;margin-right: 10px;border: solid 1px black;font-weight: bold;text-align: center;" />
        <a-button type="primary" @click="handleSampling">抽样</a-button>
        <a-button v-if="submitSample == 'true'" type="primary" @click="handleResult">确认抽样结果</a-button>
        <a-button type="primary" danger @click="handleModalClose">返回</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
      </template>
      <!--操作栏-->
      <!-- <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template> -->
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
  </BasicModal>
  <!-- 表单区域 -->
  <CommonModal @register="registerCommonModel" @success="handleSuccess" />
</template>

<script lang="ts" name="list_shi" setup>
//流程所用
import { computed, reactive, ref } from 'vue';
import CommonModal from '../../common/CommonModal.vue';
import { addShi, deleteShi, editShi, getExportUrl, getImportUrl, shenChouXiang, shiList, shiReportConfirm } from '../BtptCdsSampling.api';
import { searchShiSchema, ShiReportColumns, waitQuReportSchema, waitQuSchema } from '../BtptCdsSampling.data';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';
const { createMessage } = useMessage();
const queryParam = reactive<any>({});
const userStore = useUserStore();
const emit = defineEmits(['success']);
// 注册model
const [registerCommonModel, { openModal: openCommonModal }] = useModal();
//合计
const processedIds = ref(new Set());
const totalRecord = ref<number[]>([]);
const totalPopulation = computed(() => totalRecord.value.reduce((sum, num) => sum + num, 0));
const averagePopulation = computed(() =>
  totalRecord.value.length > 0
    ? (totalPopulation.value / totalRecord.value.length).toFixed(2)
    : 0
);
// 新增响应式变量存储抽样结果ID
const selectedSampleIds = ref<string[]>([]);
//提交状态
const submitSample = ref('false')
//抽样数
const sampleNum = ref('');
const sampleStatus = ref('false');//状态
//父组件的值
const monitorZonecode = ref('');
const monitorPoint = ref('');
const monitorOrgcode = ref('');
const pid = ref('');
// 使用 useModalInner 接收父组件传来的数据
const [registerModal, { closeModal }] = useModalInner((data) => {
  sampleStatus.value = 'false';
  submitSample.value = 'false';
  selectedSampleIds.value = [];
  if (data?.monitorZonecode) {
    monitorZonecode.value = data.monitorZonecode;
  }
  if (data?.monitorPoint) {
    monitorPoint.value = data.monitorPoint;
  }
  if (data?.pid) {
    pid.value = data.pid;
  }
  if (data?.monitorOrgcode) {
    monitorOrgcode.value = data.monitorOrgcode;
  }
  if(data?.sampleStatus){
    sampleStatus.value = data.sampleStatus;
  }
});

// 注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    // 计算总和和平均值
    afterFetch: (data) => {
      // 清空历史抽样结果
      selectedSampleIds.value = [];
      // 在数据加载完成后立即计算合计
      totalRecord.value = data.reduce((acc, record) => {
        if (record.population && !processedIds.value.has(record.id)) {
          processedIds.value.add(record.id);
          // 记录前3条数据的ID（根据实际抽样逻辑调整）
          if (selectedSampleIds.value.length < 3) {
            selectedSampleIds.value.push(record.id);
          }
          return [...acc, Number(record.population)];
        }
        return acc;
      }, []);
      return data;
    },
    rowClassName: () => '',
    title: '肿瘤报告卡表',
    api: shiList,
    pagination: false, //不分页
    columns: ShiReportColumns,
    canResize: false,
    dataSource: [],
    useSearchForm: false,// 搜索区域
    showTableSetting: false,
    showActionColumn: false,

    formConfig: {
      schemas: searchShiSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: false,
      fieldMapToNumber: [],
      fieldMapToTime: [],
    },
    actionColumn: {
      width: 150,
      fixed: 'right'
    },

    beforeFetch: (params) => {
      totalRecord.value = [];
      processedIds.value.clear();
      const newParams = Object.assign(params, queryParam);
      // 自动追加这两个字段作为查询参数
      if (monitorZonecode.value) {
        newParams.monitorZonecode = monitorZonecode.value;
      }
      if (monitorPoint.value) {
        newParams.monitorPoint = monitorPoint.value;
      }
      if (pid.value) {
        newParams.pid = pid.value;
      }
      if (monitorOrgcode.value) {
        newParams.monitorOrgcode = monitorOrgcode.value;
      }
      if (sampleStatus.value === 'true') {
        newParams.sampleNum = sampleNum.value || '3',
          sampleStatus.value = 'false',
          submitSample.value = 'true'
      }
      return newParams;
    },
  },
  exportConfig: {
    name: "肿瘤报告卡表",
    url: getExportUrl,
    params: queryParam,
  },
  importConfig: {
    url: getImportUrl,
    success: handleSuccess
  },
})

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext


/**
 * 确认按钮点击事件
 */
async function handleOk() {
  await shiReportConfirm({ pid: pid.value }, handleSuccess);
  totalRecord.value = [];
  processedIds.value.clear();
  // 关闭弹窗
  closeModal();

  // 刷新列表
  handleSuccess();
}
/**
 * 确认抽样结果
 * @param record 
 */
function handleResult(record) {
  if(selectedSampleIds.value.length <= 0){
    createMessage.warning('未满足抽样条件，请勿继续操作');
    return;
  }
  shenChouXiang({ids: selectedSampleIds.value.join(',')},handleSuccess)
  // 关闭弹窗
  closeModal();
  // 刷新列表
  handleSuccess();
  emit('success',true);
}
/**
 * 抽样
 */
function handleSampling(record) {
  sampleStatus.value = 'true';
  submitSample.value = 'true';
  // 注意：不要清空selectedSampleIds，应该在afterFetch中根据抽样逻辑设置
  sampleStatus.value = 'true'
  reload();
}

/**
 * 取消
 */
function handleCancel() {
  sampleStatus.value = 'false';
  closeModal();
}

/**
 * 新增事件
 */
function handleAdd() {
  openCommonModal(true, {
    formSchema: waitQuSchema,
    // 使用展开运算符避免引用问题
    title: '新增抽样乡镇',
    record: {
      createZone: monitorZonecode.value,
      createOrg: monitorOrgcode.value,
      monitorPoint: monitorPoint.value,
      pid: pid.value
    },
    api: addShi
  });
}

/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openCommonModal(true, {
    formSchema: waitQuReportSchema,
    record: record,
    title: '编辑乡镇信息',
    api: editShi
  })
}

/**
 * 详情
*/
function handleDetail(record: Recordable) {
  openCommonModal(true, {
    formSchema: waitQuSchema,
    record: record,
    title: '乡镇信息详情',
  })
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteShi({ id: record.id }, handleSuccess);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
  queryParam.cardStatus = undefined;
  totalRecord.value = [];
  processedIds.value.clear();
  // 触发父级更新
  emit('success');

}

/**
 * 关闭弹框时触发的方法
 */
function handleModalClose() {
  sampleStatus.value = 'false';
  submitSample.value = 'false';
  // 关闭弹框
  closeModal();
  // 刷新列表
  reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  const arr = [
    {
      label: '上报',
      onClick: handleEdit.bind(null, record),
    },
  ];
  return arr;
}
</script>

<style scoped>
:deep(.ant-picker),
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-table-row.row-red) {
  color: red;
}

:deep(.ant-table-row.row-blue) {
  color: blue;
}

:deep(.ant-table-row.row-green) {
  color: green;
}

.stats-row {
  width: 100%;
  text-align: right;
  font-weight: 600;
  /* 中等加粗 */
  padding: 8px 0;
}

/* 若需要更粗的字重 */
.stats-row strong {
  font-weight: 700;
  /* 更粗的字体 */
}
</style>