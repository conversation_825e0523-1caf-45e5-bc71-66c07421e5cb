package org.jeecg.modules.newCds.newSampling.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.newCds.newSampling.child.entity.LianTask;
import org.jeecg.modules.newCds.newSampling.child.entity.ShiTask;
import org.jeecg.modules.newCds.newSampling.child.entity.TuanTask;
import org.jeecg.modules.newCds.newSampling.child.mapper.LianTaskMapper;
import org.jeecg.modules.newCds.newSampling.child.mapper.ShiTaskMapper;
import org.jeecg.modules.newCds.newSampling.child.mapper.TuanTaskMapper;
import org.jeecg.modules.newCds.newSampling.entity.pojo.NewSampling;
import org.jeecg.modules.newCds.newSampling.enums.TaskStatusEnum;
import org.jeecg.modules.newCds.newSampling.mapper.NewSamplingMapper;
import org.jeecg.modules.newCds.newSampling.service.TaskStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 任务状态管理服务实现类
 * 实现任务状态管理相关的具体业务逻辑
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
@Slf4j
public class TaskStatusServiceImpl implements TaskStatusService {
    
    @Autowired
    private NewSamplingMapper samplingMapper;
    
    @Autowired
    private ShiTaskMapper shiMapper;
    
    @Autowired
    private TuanTaskMapper tuanMapper;
    
    @Autowired
    private LianTaskMapper lianMapper;
    
    /**
     * 更新主任务状态
     * @param taskId 任务ID
     * @param status 新状态
     */
    @Override
    @Transactional
    public void updateMainTaskStatus(String taskId, TaskStatusEnum status) {
        try {
            NewSampling task = samplingMapper.selectById(taskId);
            if (task != null) {
                task.setStatus(status.getCode());
                samplingMapper.updateById(task);
                log.info("更新主任务状态成功: taskId={}, status={}", taskId, status.getDesc());
            }
        } catch (Exception e) {
            log.error("更新主任务状态失败: taskId={}, status={}", taskId, status.getDesc(), e);
            throw e;
        }
    }

    /**
     * 更新任务状态（通用方法，兼容旧接口）
     * @param taskId 任务ID
     * @param status 新状态
     */
    @Override
    @Transactional
    public void updateTaskStatus(String taskId, TaskStatusEnum status) {
        updateMainTaskStatus(taskId, status);
    }
    
    /**
     * 更新师级任务状态
     * @param taskId 任务ID
     * @param status 新状态
     */
    @Override
    @Transactional
    public void updateShiTaskStatus(String taskId, TaskStatusEnum status) {
        try {
            ShiTask task = shiMapper.selectById(taskId);
            if (task != null) {
                task.setTaskStatus(status.getCode());
                shiMapper.updateById(task);
                log.info("更新师级任务状态成功: taskId={}, status={}", taskId, status.getDesc());
            }
        } catch (Exception e) {
            log.error("更新师级任务状态失败: taskId={}, status={}", taskId, status.getDesc(), e);
            throw e;
        }
    }
    
    /**
     * 更新团级任务状态
     * @param taskId 任务ID
     * @param status 新状态
     */
    @Override
    @Transactional
    public void updateTuanTaskStatus(String taskId, TaskStatusEnum status) {
        try {
            TuanTask task = tuanMapper.selectById(taskId);
            if (task != null) {
                task.setStatus(status.getCode());
                tuanMapper.updateById(task);
                log.info("更新团级任务状态成功: taskId={}, status={}", taskId, status.getDesc());
            }
        } catch (Exception e) {
            log.error("更新团级任务状态失败: taskId={}, status={}", taskId, status.getDesc(), e);
            throw e;
        }
    }
    
    /**
     * 更新连级任务状态
     * @param taskId 任务ID
     * @param status 新状态
     */
    @Override
    @Transactional
    public void updateLianTaskStatus(String taskId, TaskStatusEnum status) {
        try {
            LianTask task = lianMapper.selectById(taskId);
            if (task != null) {
                task.setStatus(status.getCode());
                lianMapper.updateById(task);
                log.info("更新连级任务状态成功: taskId={}, status={}", taskId, status.getDesc());
            }
        } catch (Exception e) {
            log.error("更新连级任务状态失败: taskId={}, status={}", taskId, status.getDesc(), e);
            throw e;
        }
    }
    
    /**
     * 根据任务层级和当前状态，更新到下一个状态
     * @param taskId 任务ID
     * @param layerNum 任务层级
     */
    @Override
    @Transactional
    public void updateToNextStatus(String taskId, String layerNum) {
        try {
            NewSampling task = samplingMapper.selectById(taskId);
            if (task != null) {
                String currentStatus = task.getStatus();
                TaskStatusEnum nextStatus = TaskStatusEnum.getNextLevelStatus(currentStatus);
                updateMainTaskStatus(taskId, nextStatus);
            }
        } catch (Exception e) {
            log.error("更新任务到下一状态失败: taskId={}, layerNum={}", taskId, layerNum, e);
            throw e;
        }
    }
    
    /**
     * 当开始数据上报时，更新任务状态为待抽取
     * @param taskId 任务ID
     */
    @Override
    @Transactional
    public void updateToWaitingSampling(String taskId) {
        updateMainTaskStatus(taskId, TaskStatusEnum.WAITING_SAMPLING);
    }
    
    /**
     * 确认抽样完成，更新任务状态为已完成
     * @param taskId 任务ID
     */
    @Override
    @Transactional
    public void updateToCompleted(String taskId) {
        updateMainTaskStatus(taskId, TaskStatusEnum.COMPLETED);
    }
    
    /**
     * 根据任务层级初始化任务状态
     * @param taskId 任务ID
     * @param layerNum 任务层级
     */
    @Override
    @Transactional
    public void initTaskStatus(String taskId, String layerNum) {
        TaskStatusEnum initialStatus = TaskStatusEnum.getWaitingReportStatus(layerNum);
        updateMainTaskStatus(taskId, initialStatus);
    }
    
    /**
     * 获取任务当前状态描述
     * @param taskId 任务ID
     * @return 状态描述
     */
    @Override
    public String getTaskStatusDesc(String taskId) {
        try {
            NewSampling task = samplingMapper.selectById(taskId);
            if (task != null) {
                return TaskStatusEnum.getDescByCode(task.getStatus());
            }
            return "任务不存在";
        } catch (Exception e) {
            log.error("获取任务状态描述失败: taskId={}", taskId, e);
            return "获取状态失败";
        }
    }
}
