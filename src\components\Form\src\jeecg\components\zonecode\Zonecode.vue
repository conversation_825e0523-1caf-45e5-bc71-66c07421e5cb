<!--
 *
 * inputZonecode: 回显值,默认当前用户所属地区,通过currzonecode传输给后台
 * topLevel：最高级别，0：国家，1：省，2：地市，3：区县，4：乡镇 默认当前用户所属地区
 * downLevel：最低级别，0：国家，1：省，2：地市，3：区县，4：乡镇   默认4：乡镇 
 * showUnknown：false:不加载不详，其他值：加载；默认加载不详
 *
 *-->
<template>
  <Cascader v-bind="attrs"   
     :loadData="loadData"
        lazy
  :value="cascaderValue" :options="getOptions" @change="handleChange"/> 
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, reactive, watchEffect, computed,provide, unref, watch, onMounted } from 'vue';
  import { Cascader } from 'ant-design-vue';
  import { provinceAndCityData, regionData, provinceAndCityDataPlus, regionDataPlus } from '../../../utils/areaDataUtil';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { isArray } from '/@/utils/is';
  import { getZonelevel ,getZonecodes} from '/@/utils/zone';
   import { defHttp } from '/@/utils/http/axios';
   enum Api {
     getData = '/sys/zonecode/qryZonecode' 
   }
  //用户信息
 import {useUserStore} from '/@/store/modules/user'
  export default defineComponent({
    name: 'JAreaLinkage',
    components: {
      Cascader,
    },
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.object, propTypes.array, propTypes.string]),
      topLevel: undefined,//最高级别默认为当前用户级别
      downLevel: undefined, //默认加载到接到 
      showUnknown:propTypes.string.def('false'),
      parentCode:propTypes.string.def('') 
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, { emit, refs }) {
      const userInfo = useUserStore().getUserInfo //用户信息
      const emitData = ref<any[]>([]);
      const attrs = useAttrs();
      const selectedValue = ref([]);
      // const [state] = useRuleFormItem(props, 'value', 'change', emitData);
      const cascaderValue = ref([]);
      const getOptions =  ref<any[]>([]);
      //下拉框选项值
      const selectOptions = ref<SelectValue>([]);
      //下发 selectOptions,让orgcode组件接收
     // provide('zonecode', cascaderValue);
      setTimeout(() => {
      loadRoot();
       }, 500);
     /* watch(
       () => props.parentCode,
       () => loadRoot(),
       { deep: true, immediate: true }
     ); */ 
      /**
       * 监听value变化
       */
      
      /* watchEffect(() => {
        // update-begin--author:liaozhiyang---date:20240612--for：【TV360X-1223】省市区换新组件
        if (cascaderValue.value) {
          console.log(cascaderValue.value)
           //emit('options-change', selectOptions.value);
        }  
        // update-end--author:liaozhiyang---date:20240612---for：【TV360X-1223】省市区换新组件
      }); */
      
      watchEffect(() => {
        // update-begin--author:liaozhiyang---date:20240612--for：【TV360X-1223】省市区换新组件
        if (props.value) {
          initValue();
          //provide('zonecode', targetOption.value);
           //emit('options-change', cascaderValue.value);
        } else {
          cascaderValue.value = [];
        }
        // update-end--author:liaozhiyang---date:20240612---for：【TV360X-1223】省市区换新组件
      });

     async function loadRoot() {
       const params = { inputZonecode:props.value||userInfo.zonecode,
              topLevel:props.topLevel || getZonelevel(userInfo.zonecode),
             	downLevel:props.downLevel||'3',
             	showUnknown:props.showUnknown
             }
        let result = await defHttp.post({ url: Api.getData, params });
        getOptions.value = [...result];
         cascaderValue.value= getZonecodes(params.inputZonecode,parseInt(params.topLevel))
      }
      
      /**
       * 将字符串值转化为数组
       */
      function initValue() {
          //setTimeout(() => {
           let value = props.value ? props.value : [];
           // update-begin--author:liaozhiyang---date:20240607---for：【TV360X-501】省市区换新组件
           if (value && typeof value === 'string' && value != 'null' && value != 'undefined') {
            // const arr = value.split(',');
            // cascaderValue.value = arr;
            cascaderValue.value = getZonecodes(value,parseInt(props.topLevel|| getZonelevel(userInfo.zonecode)))
           } else if (isArray(value)) {
             if (value.length) {
               cascaderValue.value = value;
             } else {
               cascaderValue.value = [];
             } 
           }
           //}, 1000);
       
        // update-end--author:liaozhiyang---date:20240607---for：【TV360X-501】省市区换新组件
      } 
      //点击最后一级时触发
      function handleChange(arr, ...args) {
        // update-begin--author:liaozhiyang---date:20240607---for：【TV360X-501】省市区换新组件
        if (arr?.length) {
          let result: any = [];
          result = arr;
          console.log(result)
          emit('options-change', result[result.length-1]);
          emit('change', result[result.length-1]);
          emit('update:value', result[result.length-1]);
        } else {
            emit('options-change', arr[arr.length-1]);
          emit('change', arr[arr.length-1]);
          emit('update:value', arr[arr.length-1]);
        }
        // update-end--author:liaozhiyang---date:20240607---for：【TV360X-501】省市区换新组件
        // emitData.value = args;
        //update-begin-author:taoyan date:2022-6-27 for: VUEN-1424【vue3】树表、单表、jvxe、erp 、内嵌子表省市县 选择不上
        // 上面改的v-model:value导致选中数据没有显示
        // state.value = result;
        //update-end-author:taoyan date:2022-6-27 for: VUEN-1424【vue3】树表、单表、jvxe、erp 、内嵌子表省市县 选择不上
      }
      
      
      // 懒加载方法
      const loadData = async (selectedOptions) => {
        const targetOption = selectedOptions[selectedOptions.length - 1];
        targetOption.loading = true;
        try {
          const params =  {
                      inputZonecode:null,
                      zonecode:targetOption.value,
                       topLevel:props.topLevel||getZonelevel(userInfo.zonecode),
                      	downLevel:props.downLevel||'3',
                      	showUnknown:props.showUnknown
                      }
          let areaList = await defHttp.post({ url: Api.getData, params });
           targetOption.children  = areaList
           cascaderValue.value = getZonecodes(params.zonecode,parseInt(params.topLevel))
          targetOption.loading = false;
          //值改变事件
          emit('options-change', targetOption.value);
          emit('change', cascaderValue.value);
        } catch (e) {
          targetOption.loading = false;
          console.error(e);
        }
      };
      
      return { 
        cascaderValue,
        attrs,
        regionData,
        getOptions,
        handleChange,
        loadData
      };
    },
  });
</script>
