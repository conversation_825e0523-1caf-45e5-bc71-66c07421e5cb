package org.jeecg.modules.newCds.newSampling.child.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 抽样子表团级
 * @Author: jeecg-boot
 * @Date:   2025-05-23
 * @Version: V1.0
 */
@Schema(description="抽样子表团级")
@Data
@TableName("btpt_cds_sampling_task_tuan")
public class TuanTask implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**年度*/
    @Schema(description = "年度")
    private String years;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "创建日期")
    private Date createTime;
    @Schema(description = "师级任务id")
    private String shiId;
    @Schema(description = "兵团任务id")
    private String btId;
    @Schema(description = "任务名称")
    private String name;
    @Schema(description = "当前状态")
    @Dict(dicCode = "cds_sampling_task_status")
    private String status;
    @Schema(description = "任务进度")
    private String progress;
    @Schema(description = "任务地区")
    private String sampleZone;
    @Schema(description = "任务机构")
    private String sampleOrg;
    @Schema(description = "需报人数")
    private String sampleNum;
    @Schema(description = "实报人数")
    private String realNum;
    @Schema(description = "常驻人口数")
    private String normalNum;
    @Schema(description = "任务等级")
    @Dict(dicCode = "btpt_cds_task_level")
    private String taskLevel;
}
