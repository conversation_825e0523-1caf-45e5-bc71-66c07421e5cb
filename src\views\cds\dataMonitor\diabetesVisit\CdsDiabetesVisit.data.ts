import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '卡片编号',
    align:"center",
    dataIndex: 'patientId'
   },
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'fullName'
   },
   {
    title: ' 性别',
    align:"center",
    dataIndex: 'gender'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'dateBirth'
   },
   {
    title: '联系电话',
    align:"center",
    dataIndex: 'contactNumber'
   },
   {
    title: '地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '糖尿病诊断日期',
    align:"center",
    dataIndex: 'diagnosisDate'
   },
   {
    title: '糖尿病类型',
    align:"center",
    dataIndex: 'typeOfDiabetes'
   },
   {
    title: '既往病史',
    align:"center",
    dataIndex: 'medicalHistory'
   },
   {
    title: '家族病史',
    align:"center",
    dataIndex: 'familyHistory'
   },
   {
    title: '过敏史',
    align:"center",
    dataIndex: 'allergyHistory'
   },
   {
    title: '吸烟状况',
    align:"center",
    dataIndex: 'smokingStatus'
   },
   {
    title: '饮酒情况',
    align:"center",
    dataIndex: 'alcoholConsumption'
   },
   {
    title: '饮食习惯',
    align:"center",
    dataIndex: 'dietaryHabits'
   },
   {
    title: '体重',
    align:"center",
    dataIndex: 'weight'
   },
   {
    title: '身高',
    align:"center",
    dataIndex: 'height'
   },
   {
    title: '体质指数',
    align:"center",
    dataIndex: 'bodyMassIndex'
   },
   {
    title: '空腹血糖',
    align:"center",
    dataIndex: 'fastingBloodGlucose'
   },
   {
    title: '随机血糖',
    align:"center",
    dataIndex: 'randomBloodGlucose'
   },
   {
    title: '糖化血红蛋白',
    align:"center",
    dataIndex: 'hemoglobinA1c'
   },
   {
    title: '尿微量白蛋白',
    align:"center",
    dataIndex: 'urineAlbumin'
   },
   {
    title: '肌酐水平',
    align:"center",
    dataIndex: 'creatinineLevel'
   },
   {
    title: '血脂谱',
    align:"center",
    dataIndex: 'lipidProfile'
   },
   {
    title: '药物名称',
    align:"center",
    dataIndex: 'medicationName'
   },
   {
    title: '药物剂量',
    align:"center",
    dataIndex: 'medicationDosage'
   },
   {
    title: '用药频率',
    align:"center",
    dataIndex: 'medicationFrequency'
   },
   {
    title: '是否使用胰岛素',
    align:"center",
    dataIndex: 'insulinUse'
   },
   {
    title: '胰岛素剂量',
    align:"center",
    dataIndex: 'insulinDosage'
   },
   {
    title: '随访日期',
    align:"center",
    dataIndex: 'followUpDate'
   },
   {
    title: '下次随访日期',
    align:"center",
    dataIndex: 'nextFollowUpDate'
   },
   {
    title: '医生建议',
    align:"center",
    dataIndex: 'doctorRecommendations'
   },
   {
    title: '生活方式改变建议',
    align:"center",
    dataIndex: 'lifestyleModifications'
   },
   {
    title: '药物调整建议',
    align:"center",
    dataIndex: 'medicationChanges'
   },
   {
    title: '患者依从性',
    align:"center",
    dataIndex: 'patientCompliance'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'notes'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '患者ID',
    field: 'patientId',
    component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'fullName',
    component: 'Input',
  },
  {
    label: ' 性别',
    field: 'gender',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"sex"
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '患者ID',
    field: 'patientId',
    component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'fullName',
    component: 'Input',
  },
  {
    label: ' 性别',
    field: 'gender',
    component: 'Input',
  },
  {
    label: '出生日期',
    field: 'dateBirth',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '联系电话',
    field: 'contactNumber',
    component: 'Input',
  },
  {
    label: '地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '糖尿病诊断日期',
    field: 'diagnosisDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '糖尿病类型',
    field: 'typeOfDiabetes',
    component: 'Input',
  },
  {
    label: '既往病史',
    field: 'medicalHistory',
    component: 'Input',
  },
  {
    label: '家族病史',
    field: 'familyHistory',
    component: 'Input',
  },
  {
    label: '过敏史',
    field: 'allergyHistory',
    component: 'Input',
  },
  {
    label: '吸烟状况',
    field: 'smokingStatus',
    component: 'Input',
  },
  {
    label: '饮酒情况',
    field: 'alcoholConsumption',
    component: 'Input',
  },
  {
    label: '饮食习惯',
    field: 'dietaryHabits',
    component: 'Input',
  },
  {
    label: '体重',
    field: 'weight',
    component: 'Input',
  },
  {
    label: '身高',
    field: 'height',
    component: 'Input',
  },
  {
    label: '体质指数',
    field: 'bodyMassIndex',
    component: 'Input',
  },
  {
    label: '空腹血糖',
    field: 'fastingBloodGlucose',
    component: 'Input',
  },
  {
    label: '随机血糖',
    field: 'randomBloodGlucose',
    component: 'Input',
  },
  {
    label: '糖化血红蛋白',
    field: 'hemoglobinA1c',
    component: 'Input',
  },
  {
    label: '尿微量白蛋白',
    field: 'urineAlbumin',
    component: 'Input',
  },
  {
    label: '肌酐水平',
    field: 'creatinineLevel',
    component: 'Input',
  },
  {
    label: '血脂谱',
    field: 'lipidProfile',
    component: 'Input',
  },
  {
    label: '药物名称',
    field: 'medicationName',
    component: 'Input',
  },
  {
    label: '药物剂量',
    field: 'medicationDosage',
    component: 'Input',
  },
  {
    label: '用药频率',
    field: 'medicationFrequency',
    component: 'Input',
  },
  {
    label: '是否使用胰岛素',
    field: 'insulinUse',
    component: 'Input',
  },
  {
    label: '胰岛素剂量',
    field: 'insulinDosage',
    component: 'Input',
  },
  {
    label: '随访日期',
    field: 'followUpDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '下次随访日期',
    field: 'nextFollowUpDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '医生建议',
    field: 'doctorRecommendations',
    component: 'Input',
  },
  {
    label: '生活方式改变建议',
    field: 'lifestyleModifications',
    component: 'Input',
  },
  {
    label: '药物调整建议',
    field: 'medicationChanges',
    component: 'Input',
  },
  {
    label: '患者依从性',
    field: 'patientCompliance',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'notes',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  patientId: {title: '患者ID',order: 0,view: 'text', type: 'string',},
  fullName: {title: '患者姓名',order: 1,view: 'text', type: 'string',},
  gender: {title: ' 性别',order: 2,view: 'text', type: 'string',},
  dateBirth: {title: '出生日期',order: 3,view: 'datetime', type: 'string',},
  contactNumber: {title: '联系电话',order: 4,view: 'text', type: 'string',},
  address: {title: '地址',order: 5,view: 'text', type: 'string',},
  diagnosisDate: {title: '糖尿病诊断日期',order: 6,view: 'datetime', type: 'string',},
  typeOfDiabetes: {title: '糖尿病类型',order: 7,view: 'text', type: 'string',},
  medicalHistory: {title: '既往病史',order: 8,view: 'text', type: 'string',},
  familyHistory: {title: '家族病史',order: 9,view: 'text', type: 'string',},
  allergyHistory: {title: '过敏史',order: 10,view: 'text', type: 'string',},
  smokingStatus: {title: '吸烟状况',order: 11,view: 'text', type: 'string',},
  alcoholConsumption: {title: '饮酒情况',order: 12,view: 'text', type: 'string',},
  dietaryHabits: {title: '饮食习惯',order: 13,view: 'text', type: 'string',},
  weight: {title: '体重',order: 14,view: 'text', type: 'string',},
  height: {title: '身高',order: 15,view: 'text', type: 'string',},
  bodyMassIndex: {title: '体质指数',order: 16,view: 'text', type: 'string',},
  fastingBloodGlucose: {title: '空腹血糖',order: 17,view: 'text', type: 'string',},
  randomBloodGlucose: {title: '随机血糖',order: 18,view: 'text', type: 'string',},
  hemoglobinA1c: {title: '糖化血红蛋白',order: 19,view: 'text', type: 'string',},
  urineAlbumin: {title: '尿微量白蛋白',order: 20,view: 'text', type: 'string',},
  creatinineLevel: {title: '肌酐水平',order: 21,view: 'text', type: 'string',},
  lipidProfile: {title: '血脂谱',order: 22,view: 'text', type: 'string',},
  medicationName: {title: '药物名称',order: 23,view: 'text', type: 'string',},
  medicationDosage: {title: '药物剂量',order: 24,view: 'text', type: 'string',},
  medicationFrequency: {title: '用药频率',order: 25,view: 'text', type: 'string',},
  insulinUse: {title: '是否使用胰岛素',order: 26,view: 'text', type: 'string',},
  insulinDosage: {title: '胰岛素剂量',order: 27,view: 'text', type: 'string',},
  followUpDate: {title: '随访日期',order: 28,view: 'datetime', type: 'string',},
  nextFollowUpDate: {title: '下次随访日期',order: 29,view: 'datetime', type: 'string',},
  doctorRecommendations: {title: '医生建议',order: 30,view: 'text', type: 'string',},
  lifestyleModifications: {title: '生活方式改变建议',order: 31,view: 'text', type: 'string',},
  medicationChanges: {title: '药物调整建议',order: 32,view: 'text', type: 'string',},
  patientCompliance: {title: '患者依从性',order: 33,view: 'text', type: 'string',},
  notes: {title: '备注',order: 34,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}