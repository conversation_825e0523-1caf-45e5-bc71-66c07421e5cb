package org.jeecg.modules.newCds.newSampling.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.newCds.newSampling.entity.pojo.NewSampling;
import org.jeecg.modules.newCds.newSampling.entity.vo.ZoneVo;

import java.util.List;

/**
 * @Description: 慢病抽样任务
 * @Author: jeecg-boot
 * @Date:   2025-06-30
 * @Version: V1.0
 */
public interface NewSamplingMapper extends BaseMapper<NewSampling> {
    //兵团级抽样分页
//    @Select("select b.id, a.popular,b.code,b.full_name from btpt_cds_popular a left join dd_zone b on a.code = b.code ORDER BY a.code asc")
    List<ZoneVo> queryShi(String removeIds,String layerNum);

    //获取地区数据
    List<ZoneVo> selectZoneByIds(String ids);
}
